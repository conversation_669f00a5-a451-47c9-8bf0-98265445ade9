<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>PhonePe Integration Test - Matrimony Site</title>
    
    <!-- CSS Files -->
    <link href="css/bootstrap.css" rel="stylesheet">
    <link href="css/custom.css" rel="stylesheet">
    <link href="css/icon-fixes.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/48403ccd1a.js" crossorigin="anonymous"></script>
    
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: green; background: #e8f5e8; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid green; }
        .error { color: red; background: #ffe8e8; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid red; }
        .warning { color: orange; background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid orange; }
        .info { color: blue; background: #e8f0ff; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid blue; }
        .btn { display: inline-block; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; border: none; border-radius: 5px; margin: 5px; cursor: pointer; }
        .btn:hover { background: #005a87; text-decoration: none; color: white; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; }
        .btn-phonepe { background: linear-gradient(135deg, #5f27cd 0%, #7c4dff 100%); }
        .test-section { background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #007cba; }
        .feature-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0; }
        .feature-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; }
        .feature-card i { font-size: 36px; color: #5f27cd; margin-bottom: 15px; }
        .code-block { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 13px; margin: 10px 0; overflow-x: auto; }
        .status-badge { padding: 5px 12px; border-radius: 15px; font-size: 12px; font-weight: 600; }
        .status-success { background: #d4edda; color: #155724; }
        .status-error { background: #f8d7da; color: #721c24; }
        .status-pending { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 PhonePe Payment Gateway Integration Test</h1>
        <p class="text-muted">Comprehensive testing for PhonePe payment integration</p>
        
        <!-- Integration Status -->
        <div class="success">
            <h3>✅ PhonePe Integration Status</h3>
            <p><strong>Integration:</strong> Complete and ready for testing</p>
            <p><strong>Files Created:</strong> 7 files for complete PhonePe integration</p>
            <p><strong>Database:</strong> Tables updated with PhonePe support</p>
            <p><strong>Admin Panel:</strong> Configuration interface ready</p>
        </div>

        <!-- Files Created -->
        <div class="test-section">
            <h3><i class="fa fa-file-code"></i> Integration Files Created</h3>
            <div class="row">
                <div class="col-md-6">
                    <h4>Core Integration Files:</h4>
                    <ul>
                        <li><strong>phonepe_integration.php</strong> - Main PhonePe class</li>
                        <li><strong>phonepe_payment_form.php</strong> - Payment form</li>
                        <li><strong>phonepe_callback.php</strong> - Payment callback handler</li>
                        <li><strong>phonepe_webhook.php</strong> - Webhook handler</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h4>Admin & Setup Files:</h4>
                    <ul>
                        <li><strong>premium_admin/PhonePeSettings.php</strong> - Admin config</li>
                        <li><strong>phonepe_database_setup.sql</strong> - Database setup</li>
                        <li><strong>test_phonepe_integration.php</strong> - This test page</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Features Overview -->
        <div class="test-section">
            <h3><i class="fa fa-star"></i> PhonePe Integration Features</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <i class="fa fa-shield-alt"></i>
                    <h4>Secure Payments</h4>
                    <p>SHA256 checksum validation and secure API communication</p>
                </div>
                <div class="feature-card">
                    <i class="fa fa-mobile-alt"></i>
                    <h4>Multiple Payment Methods</h4>
                    <p>UPI, Credit/Debit Cards, Net Banking, Wallets</p>
                </div>
                <div class="feature-card">
                    <i class="fa fa-webhook"></i>
                    <h4>Webhook Support</h4>
                    <p>Real-time payment status updates via webhooks</p>
                </div>
                <div class="feature-card">
                    <i class="fa fa-cog"></i>
                    <h4>Admin Configuration</h4>
                    <p>Easy setup through admin panel interface</p>
                </div>
                <div class="feature-card">
                    <i class="fa fa-bug"></i>
                    <h4>Debug & Logging</h4>
                    <p>Comprehensive logging for troubleshooting</p>
                </div>
                <div class="feature-card">
                    <i class="fa fa-undo"></i>
                    <h4>Refund Support</h4>
                    <p>Built-in refund processing capability</p>
                </div>
            </div>
        </div>

        <!-- Setup Instructions -->
        <div class="info">
            <h3>🚀 Setup Instructions</h3>
            <ol>
                <li><strong>Database Setup:</strong> Run the SQL commands from <code>phonepe_database_setup.sql</code></li>
                <li><strong>PhonePe Account:</strong> Create merchant account at <a href="https://developer.phonepe.com" target="_blank">PhonePe Developer Portal</a></li>
                <li><strong>Get Credentials:</strong> Obtain Merchant ID, Salt Key, and Salt Index</li>
                <li><strong>Configure:</strong> Use admin panel at <code>premium_admin/PhonePeSettings.php</code></li>
                <li><strong>Test:</strong> Test in sandbox mode before going live</li>
                <li><strong>Go Live:</strong> Switch to production mode when ready</li>
            </ol>
        </div>

        <!-- Database Setup -->
        <div class="test-section">
            <h3><i class="fa fa-database"></i> Database Setup</h3>
            <p>Run these SQL commands to set up PhonePe integration:</p>
            <div class="code-block">
-- Add PhonePe to payment methods
INSERT IGNORE INTO payment_method (pay_id, pay_name, status) VALUES ('4', 'PhonePe', 'PENDING');

-- Add PhonePe configuration columns
ALTER TABLE payment_method 
ADD COLUMN IF NOT EXISTS phonepe_merchant_id VARCHAR(255) DEFAULT '',
ADD COLUMN IF NOT EXISTS phonepe_salt_key TEXT DEFAULT '',
ADD COLUMN IF NOT EXISTS phonepe_salt_index INT DEFAULT 1,
ADD COLUMN IF NOT EXISTS phonepe_is_production ENUM('YES', 'NO') DEFAULT 'NO';

-- Update payments table for longer transaction IDs
ALTER TABLE payments MODIFY COLUMN pay_id VARCHAR(100);
            </div>
        </div>

        <!-- API Configuration -->
        <div class="test-section">
            <h3><i class="fa fa-key"></i> API Configuration</h3>
            <div class="row">
                <div class="col-md-6">
                    <h4>Sandbox (Testing):</h4>
                    <ul>
                        <li><strong>API URL:</strong> api-preprod.phonepe.com</li>
                        <li><strong>Purpose:</strong> Testing and development</li>
                        <li><strong>Real Money:</strong> No</li>
                        <li><strong>Test Cards:</strong> Available</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h4>Production (Live):</h4>
                    <ul>
                        <li><strong>API URL:</strong> api.phonepe.com</li>
                        <li><strong>Purpose:</strong> Live transactions</li>
                        <li><strong>Real Money:</strong> Yes</li>
                        <li><strong>Approval:</strong> Required from PhonePe</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Webhook Configuration -->
        <div class="warning">
            <h3><i class="fa fa-webhook"></i> Webhook Configuration</h3>
            <p>Configure these URLs in your PhonePe merchant dashboard:</p>
            <div class="code-block">
Redirect URL: <?php echo 'https://' . $_SERVER['HTTP_HOST'] . '/phonepe_callback.php'; ?>
Webhook URL: <?php echo 'https://' . $_SERVER['HTTP_HOST'] . '/phonepe_webhook.php'; ?>
            </div>
            <p><strong>Note:</strong> Webhooks require HTTPS. Ensure your website has a valid SSL certificate.</p>
        </div>

        <!-- Testing Checklist -->
        <div class="test-section">
            <h3><i class="fa fa-check-square"></i> Testing Checklist</h3>
            <div class="row">
                <div class="col-md-6">
                    <h4>Before Testing:</h4>
                    <ul>
                        <li>☐ Database tables updated</li>
                        <li>☐ PhonePe credentials configured</li>
                        <li>☐ Sandbox mode enabled</li>
                        <li>☐ SSL certificate installed</li>
                        <li>☐ Webhook URLs configured</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h4>Test Scenarios:</h4>
                    <ul>
                        <li>☐ Successful payment</li>
                        <li>☐ Failed payment</li>
                        <li>☐ Cancelled payment</li>
                        <li>☐ Webhook notifications</li>
                        <li>☐ Database updates</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Payment Flow -->
        <div class="info">
            <h3><i class="fa fa-flow-chart"></i> Payment Flow</h3>
            <ol>
                <li><strong>User selects plan</strong> → Goes to payment options</li>
                <li><strong>Clicks PhonePe</strong> → Redirected to phonepe_payment_form.php</li>
                <li><strong>Confirms payment</strong> → API call to PhonePe</li>
                <li><strong>PhonePe processing</strong> → User completes payment</li>
                <li><strong>Payment result</strong> → Redirected to phonepe_callback.php</li>
                <li><strong>Status verification</strong> → API call to verify payment</li>
                <li><strong>Database update</strong> → Membership activated</li>
                <li><strong>Webhook notification</strong> → Additional confirmation</li>
            </ol>
        </div>

        <!-- Security Features -->
        <div class="success">
            <h3><i class="fa fa-lock"></i> Security Features</h3>
            <ul>
                <li><strong>Checksum Validation:</strong> SHA256 signature verification</li>
                <li><strong>Secure Communication:</strong> HTTPS only</li>
                <li><strong>Transaction Verification:</strong> Server-side status check</li>
                <li><strong>Webhook Validation:</strong> Signature verification for webhooks</li>
                <li><strong>Data Encryption:</strong> Sensitive data protection</li>
                <li><strong>Session Management:</strong> Secure session handling</li>
            </ul>
        </div>

        <!-- Quick Links -->
        <div class="test-section">
            <h3><i class="fa fa-link"></i> Quick Links</h3>
            <div class="text-center">
                <a href="premium_admin/PhonePeSettings.php" class="btn btn-phonepe">
                    <i class="fa fa-cog"></i> Configure PhonePe
                </a>
                <a href="paymentOptions.php?pid=1" class="btn btn-success">
                    <i class="fa fa-credit-card"></i> Test Payment
                </a>
                <a href="membershipplans.php" class="btn btn-warning">
                    <i class="fa fa-crown"></i> View Plans
                </a>
                <a href="https://developer.phonepe.com/v1/docs/introduction-pg" target="_blank" class="btn btn-info">
                    <i class="fa fa-book"></i> PhonePe Docs
                </a>
            </div>
        </div>

        <!-- Support Information -->
        <div class="info">
            <h3><i class="fa fa-headset"></i> Support & Documentation</h3>
            <div class="row">
                <div class="col-md-6">
                    <h4>PhonePe Resources:</h4>
                    <ul>
                        <li><a href="https://developer.phonepe.com/v1/docs/introduction-pg" target="_blank">API Documentation</a></li>
                        <li><a href="https://developer.phonepe.com/v1/docs/payment-gateway-integration" target="_blank">Integration Guide</a></li>
                        <li><a href="https://developer.phonepe.com/v1/docs/webhooks" target="_blank">Webhook Guide</a></li>
                        <li><a href="mailto:<EMAIL>">PhonePe Support</a></li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h4>Integration Files:</h4>
                    <ul>
                        <li><code>phonepe_integration.php</code> - Core integration</li>
                        <li><code>phonepe_payment_form.php</code> - Payment form</li>
                        <li><code>phonepe_callback.php</code> - Callback handler</li>
                        <li><code>premium_admin/PhonePeSettings.php</code> - Admin panel</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center" style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">
            <p class="text-muted">
                <strong>PhonePe Integration Test Page</strong> - 
                Ready for configuration and testing
            </p>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/jquery.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script>
        $(document).ready(function() {
            console.log('✅ PhonePe Integration Test Page Loaded');
            console.log('📱 PhonePe integration is ready for configuration');
            console.log('🔧 Configure at: premium_admin/PhonePeSettings.php');
        });
    </script>
</body>
</html>
