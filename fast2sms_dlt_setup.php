<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Fast2SMS DLT Route Setup - Immediate Solution</title>
    <link href="css/bootstrap.css" rel="stylesheet">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: green; background: #e8f5e8; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid green; }
        .error { color: red; background: #ffe8e8; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid red; }
        .warning { color: orange; background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid orange; }
        .info { color: blue; background: #e8f0ff; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid blue; }
        .btn { display: inline-block; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; border: none; border-radius: 5px; margin: 5px; cursor: pointer; }
        .btn:hover { background: #005a87; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; }
        .step { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #28a745; }
        .step h3 { color: #28a745; margin-top: 0; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Fast2SMS DLT Route - Immediate Solution</h1>
        
        <div class="success">
            <h3>✅ Good News!</h3>
            <p>While waiting for OTP route approval, you can use <strong>Fast2SMS DLT route</strong> immediately with your existing account!</p>
            <p><strong>Your Account:</strong> Already verified and ready for DLT</p>
            <p><strong>Setup Time:</strong> 5-10 minutes</p>
        </div>

        <div class="info">
            <h3>🔍 What is DLT Route?</h3>
            <ul>
                <li><strong>DLT:</strong> Distributed Ledger Technology (TRAI approved)</li>
                <li><strong>Same Account:</strong> Use your existing Fast2SMS account</li>
                <li><strong>Same API Key:</strong> r2oeVHLQzpadwlNAGiEXtBfguK1IWS7YxZ58ROqn3DhMFvy9UbjN4C6iqf59nFPW8yD0wuZslmAJRtOr</li>
                <li><strong>Only Difference:</strong> Need a registered sender ID</li>
            </ul>
        </div>

        <h2>📋 DLT Setup Steps:</h2>

        <div class="step">
            <h3>Step 1: Login to Fast2SMS Dashboard</h3>
            <ol>
                <li>Go to <a href="https://www.fast2sms.com/login" target="_blank" class="btn btn-success">Fast2SMS Dashboard</a></li>
                <li>Login with your existing credentials</li>
                <li>Navigate to the main dashboard</li>
            </ol>
        </div>

        <div class="step">
            <h3>Step 2: Access DLT Section</h3>
            <ol>
                <li>Look for <strong>"DLT"</strong> or <strong>"DLT SMS"</strong> in the menu</li>
                <li>Click on <strong>"Sender ID Registration"</strong></li>
                <li>You should see a form to register sender ID</li>
            </ol>
        </div>

        <div class="step">
            <h3>Step 3: Register Sender ID</h3>
            <p><strong>Sender ID Requirements:</strong></p>
            <ul>
                <li><strong>Length:</strong> Exactly 6 characters</li>
                <li><strong>Format:</strong> Only alphabets (A-Z)</li>
                <li><strong>Suggestions for Matrimony:</strong></li>
                <ul>
                    <li><strong>PELLIP</strong> (from pellipusthakam)</li>
                    <li><strong>MATRIM</strong> (matrimony)</li>
                    <li><strong>WEDLOK</strong> (wedding)</li>
                    <li><strong>SHAADI</strong> (marriage)</li>
                </ul>
            </ul>
            
            <div class="warning">
                <p><strong>Recommended:</strong> Use <strong>PELLIP</strong> as it matches your website name</p>
            </div>
        </div>

        <div class="step">
            <h3>Step 4: Submit Required Information</h3>
            <p>Fill in the registration form:</p>
            <ul>
                <li><strong>Sender ID:</strong> PELLIP (or your choice)</li>
                <li><strong>Business Type:</strong> Matrimony Services</li>
                <li><strong>Website:</strong> https://pellipusthakam.co.in/</li>
                <li><strong>Use Case:</strong> OTP and transactional messages</li>
                <li><strong>Sample Message:</strong> "Your OTP is 1234. Do not share with anyone. -Team Pellipusthakam"</li>
            </ul>
        </div>

        <div class="step">
            <h3>Step 5: Wait for Sender ID Approval</h3>
            <ul>
                <li><strong>Processing Time:</strong> 2-24 hours (usually same day)</li>
                <li><strong>Notification:</strong> Email when approved</li>
                <li><strong>Status:</strong> Check DLT section in dashboard</li>
            </ul>
        </div>

        <h2>🔧 Update Your Code:</h2>

        <div class="info">
            <h3>📝 DLT Route API Format</h3>
            <p>Once your sender ID is approved, update your mobile-apis.php:</p>
            <pre><?php echo htmlspecialchars('<?php
// Fast2SMS DLT Route Configuration
$authorization = "r2oeVHLQzpadwlNAGiEXtBfguK1IWS7YxZ58ROqn3DhMFvy9UbjN4C6iqf59nFPW8yD0wuZslmAJRtOr";
$sender_id = "PELLIP"; // Your approved sender ID
$message = urlencode("Your OTP is $order_id. Do not share with anyone. -Team Pellipusthakam");

$url = "https://www.fast2sms.com/dev/bulkV2?authorization=$authorization&route=dlt&sender_id=$sender_id&message=$message&variables_values=$order_id&flash=0&numbers=$mno";

// Send SMS
$response = file($url);
?>'); ?></pre>
        </div>

        <h2>🧪 Test DLT Route:</h2>

        <?php
        // Handle test form submission
        if (isset($_POST['test_dlt'])) {
            $mobile = $_POST['mobile'];
            $otp = $_POST['otp'];
            $sender_id = $_POST['sender_id'];
            
            echo "<div class='info'>";
            echo "<h3>🧪 Testing DLT Route</h3>";
            echo "<strong>Mobile:</strong> $mobile<br>";
            echo "<strong>OTP:</strong> $otp<br>";
            echo "<strong>Sender ID:</strong> $sender_id<br>";
            echo "</div>";
            
            // Build DLT URL
            $authorization = "r2oeVHLQzpadwlNAGiEXtBfguK1IWS7YxZ58ROqn3DhMFvy9UbjN4C6iqf59nFPW8yD0wuZslmAJRtOr";
            $message = urlencode("Your OTP is $otp. Do not share with anyone. -Team Pellipusthakam");
            $url = "https://www.fast2sms.com/dev/bulkV2?authorization=$authorization&route=dlt&sender_id=$sender_id&message=$message&variables_values=$otp&flash=0&numbers=$mobile";
            
            echo "<div class='info'>";
            echo "<h3>📡 DLT API Request:</h3>";
            echo "<strong>URL:</strong><br><pre>$url</pre>";
            echo "</div>";
            
            // Test API call
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            echo "<div class='info'>";
            echo "<h3>📤 API Response:</h3>";
            echo "<strong>HTTP Code:</strong> $http_code<br>";
            
            if ($error) {
                echo "<div class='error'>❌ cURL Error: $error</div>";
            } else {
                echo "<strong>Response:</strong><br><pre>$response</pre>";
                
                $response_data = json_decode($response, true);
                if ($response_data) {
                    if (isset($response_data['return']) && $response_data['return'] == true) {
                        echo "<div class='success'>✅ DLT SMS Sent Successfully!</div>";
                    } else {
                        $error_code = isset($response_data['code']) ? $response_data['code'] : 'Unknown';
                        $error_message = isset($response_data['message']) ? $response_data['message'] : 'Unknown error';
                        echo "<div class='error'>❌ DLT SMS Failed</div>";
                        echo "<strong>Error Code:</strong> $error_code<br>";
                        echo "<strong>Error Message:</strong> $error_message<br>";
                        
                        if ($error_code == '406') {
                            echo "<div class='warning'>";
                            echo "<h4>⚠️ Sender ID Not Approved</h4>";
                            echo "<p>Your sender ID '$sender_id' is not yet approved. Please:</p>";
                            echo "<ol>";
                            echo "<li>Check your Fast2SMS DLT dashboard</li>";
                            echo "<li>Wait for sender ID approval</li>";
                            echo "<li>Use an approved sender ID</li>";
                            echo "</ol>";
                            echo "</div>";
                        }
                    }
                }
            }
            echo "</div>";
        }
        ?>

        <div class="info">
            <h3>🧪 Test DLT Route</h3>
            <form method="post">
                <div class="form-group">
                    <label>Mobile Number (10 digits):</label>
                    <input type="text" name="mobile" placeholder="9876543210" required>
                </div>
                <div class="form-group">
                    <label>OTP (4-6 digits):</label>
                    <input type="text" name="otp" value="<?php echo rand(1000, 9999); ?>" required>
                </div>
                <div class="form-group">
                    <label>Sender ID:</label>
                    <select name="sender_id">
                        <option value="PELLIP">PELLIP (Recommended)</option>
                        <option value="MATRIM">MATRIM</option>
                        <option value="WEDLOK">WEDLOK</option>
                        <option value="FSTSMS">FSTSMS (Default)</option>
                    </select>
                </div>
                <button type="submit" name="test_dlt" class="btn btn-success">Test DLT SMS</button>
            </form>
        </div>

        <h2>📊 Comparison: OTP vs DLT Route</h2>

        <div class="info">
            <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
                <tr style="background: #f8f9fa;">
                    <th style="padding: 10px; text-align: left; border: 1px solid #ddd;">Feature</th>
                    <th style="padding: 10px; text-align: left; border: 1px solid #ddd;">OTP Route</th>
                    <th style="padding: 10px; text-align: left; border: 1px solid #ddd;">DLT Route</th>
                </tr>
                <tr>
                    <td style="padding: 10px; border: 1px solid #ddd;"><strong>Setup Time</strong></td>
                    <td style="padding: 10px; border: 1px solid #ddd;">24-48 hours (verification)</td>
                    <td style="padding: 10px; border: 1px solid #ddd;">2-24 hours (sender ID)</td>
                </tr>
                <tr>
                    <td style="padding: 10px; border: 1px solid #ddd;"><strong>Sender ID</strong></td>
                    <td style="padding: 10px; border: 1px solid #ddd;">Not required</td>
                    <td style="padding: 10px; border: 1px solid #ddd;">Required (6 chars)</td>
                </tr>
                <tr>
                    <td style="padding: 10px; border: 1px solid #ddd;"><strong>Message Format</strong></td>
                    <td style="padding: 10px; border: 1px solid #ddd;">Template-based</td>
                    <td style="padding: 10px; border: 1px solid #ddd;">Custom message</td>
                </tr>
                <tr>
                    <td style="padding: 10px; border: 1px solid #ddd;"><strong>Delivery Rate</strong></td>
                    <td style="padding: 10px; border: 1px solid #ddd;">95-98%</td>
                    <td style="padding: 10px; border: 1px solid #ddd;">95-98%</td>
                </tr>
                <tr>
                    <td style="padding: 10px; border: 1px solid #ddd;"><strong>Cost</strong></td>
                    <td style="padding: 10px; border: 1px solid #ddd;">Same</td>
                    <td style="padding: 10px; border: 1px solid #ddd;">Same</td>
                </tr>
            </table>
        </div>

        <div class="success">
            <h3>🎯 Recommended Action Plan</h3>
            <ol>
                <li><strong>Immediate:</strong> Setup DLT route with sender ID "PELLIP"</li>
                <li><strong>Short-term:</strong> Use DLT route for SMS delivery</li>
                <li><strong>Long-term:</strong> Switch to OTP route once approved</li>
                <li><strong>Backup:</strong> Keep DLT route as fallback option</li>
            </ol>
        </div>

        <div class="warning">
            <h3>💡 Pro Tips</h3>
            <ul>
                <li><strong>Sender ID:</strong> Choose something related to your brand</li>
                <li><strong>Message:</strong> Include your brand name for trust</li>
                <li><strong>Testing:</strong> Test with your own mobile first</li>
                <li><strong>Monitoring:</strong> Check delivery reports in dashboard</li>
            </ul>
        </div>

        <div class="text-center" style="margin-top: 30px;">
            <a href="https://www.fast2sms.com/login" target="_blank" class="btn btn-success">Setup DLT Route</a>
            <a href="fast2sms_approval_status.php" class="btn btn-warning">Check OTP Approval</a>
            <a href="premium_admin/SiteSMSSettings.php" class="btn btn-info">Update SMS Settings</a>
        </div>
    </div>
</body>
</html>
