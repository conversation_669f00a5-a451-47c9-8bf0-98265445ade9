<?php
include_once '../databaseConn.php';
include_once '../class/Config.class.php';
$configObj = new Config();
include_once '../lib/requestHandler.php';
$DatabaseCo = new DatabaseConn();

// Create theme settings table if it doesn't exist
$create_table_sql = "CREATE TABLE IF NOT EXISTS `theme_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `primary_color` varchar(7) DEFAULT '#499202',
  `secondary_color` varchar(7) DEFAULT '#e47203',
  `accent_color` varchar(7) DEFAULT '#089cbe',
  `success_color` varchar(7) DEFAULT '#00a65a',
  `warning_color` varchar(7) DEFAULT '#f39c12',
  `danger_color` varchar(7) DEFAULT '#f56954',
  `dark_color` varchar(7) DEFAULT '#222222',
  `light_color` varchar(7) DEFAULT '#f4f4f4',
  `button_primary_color` varchar(7) DEFAULT '#499202',
  `button_secondary_color` varchar(7) DEFAULT '#e47203',
  `link_color` varchar(7) DEFAULT '#089cbe',
  `link_hover_color` varchar(7) DEFAULT '#067a9b',
  `header_bg_color` varchar(7) DEFAULT '#ffffff',
  `footer_bg_color` varchar(7) DEFAULT '#2c3e50',
  `sidebar_bg_color` varchar(7) DEFAULT '#f8f9fa',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

$DatabaseCo->dbLink->query($create_table_sql);

// Insert default record if none exists
$check_record = $DatabaseCo->dbLink->query("SELECT COUNT(*) as count FROM theme_settings");
$count_result = mysqli_fetch_object($check_record);
if ($count_result->count == 0) {
    $DatabaseCo->dbLink->query("INSERT INTO theme_settings (id) VALUES (1)");
}

$msg = "";

// Handle form submission
if (isset($_REQUEST['update_theme_settings'])) {
    $primary_color = mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['primary_color']);
    $secondary_color = mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['secondary_color']);
    $accent_color = mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['accent_color']);
    $success_color = mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['success_color']);
    $warning_color = mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['warning_color']);
    $danger_color = mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['danger_color']);
    $dark_color = mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['dark_color']);
    $light_color = mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['light_color']);
    $button_primary_color = mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['button_primary_color']);
    $button_secondary_color = mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['button_secondary_color']);
    $link_color = mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['link_color']);
    $link_hover_color = mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['link_hover_color']);
    $header_bg_color = mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['header_bg_color']);
    $footer_bg_color = mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['footer_bg_color']);
    $sidebar_bg_color = mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['sidebar_bg_color']);

    $update_sql = "UPDATE theme_settings SET 
        primary_color='$primary_color',
        secondary_color='$secondary_color',
        accent_color='$accent_color',
        success_color='$success_color',
        warning_color='$warning_color',
        danger_color='$danger_color',
        dark_color='$dark_color',
        light_color='$light_color',
        button_primary_color='$button_primary_color',
        button_secondary_color='$button_secondary_color',
        link_color='$link_color',
        link_hover_color='$link_hover_color',
        header_bg_color='$header_bg_color',
        footer_bg_color='$footer_bg_color',
        sidebar_bg_color='$sidebar_bg_color'
        WHERE id='1'";

    if ($DatabaseCo->dbLink->query($update_sql)) {
        $msg = "Theme settings updated successfully.";
        
        // Generate dynamic CSS file
        generateDynamicCSS($DatabaseCo);
    } else {
        $msg = "Error updating theme settings: " . mysqli_error($DatabaseCo->dbLink);
    }
}

// Handle reset to default
if (isset($_REQUEST['reset_theme'])) {
    $reset_sql = "UPDATE theme_settings SET 
        primary_color='#499202',
        secondary_color='#e47203',
        accent_color='#089cbe',
        success_color='#00a65a',
        warning_color='#f39c12',
        danger_color='#f56954',
        dark_color='#222222',
        light_color='#f4f4f4',
        button_primary_color='#499202',
        button_secondary_color='#e47203',
        link_color='#089cbe',
        link_hover_color='#067a9b',
        header_bg_color='#ffffff',
        footer_bg_color='#2c3e50',
        sidebar_bg_color='#f8f9fa'
        WHERE id='1'";
    
    if ($DatabaseCo->dbLink->query($reset_sql)) {
        $msg = "Theme reset to default colors successfully.";
        generateDynamicCSS($DatabaseCo);
    }
}

// Function to generate dynamic CSS
function generateDynamicCSS($DatabaseCo) {
    $sql = $DatabaseCo->dbLink->query("SELECT * FROM theme_settings WHERE id='1'");
    $theme = mysqli_fetch_object($sql);
    
    if ($theme) {
        $css_content = "/* Auto-generated theme CSS - Last updated: " . date('Y-m-d H:i:s') . " */\n\n";
        
        $css_content .= ":root {\n";
        $css_content .= "  --primary-color: {$theme->primary_color};\n";
        $css_content .= "  --secondary-color: {$theme->secondary_color};\n";
        $css_content .= "  --accent-color: {$theme->accent_color};\n";
        $css_content .= "  --success-color: {$theme->success_color};\n";
        $css_content .= "  --warning-color: {$theme->warning_color};\n";
        $css_content .= "  --danger-color: {$theme->danger_color};\n";
        $css_content .= "  --dark-color: {$theme->dark_color};\n";
        $css_content .= "  --light-color: {$theme->light_color};\n";
        $css_content .= "}\n\n";
        
        $css_content .= "/* Primary Theme Colors */\n";
        $css_content .= ".inThemeGreen, .gt-text-green { color: {$theme->primary_color} !important; }\n";
        $css_content .= ".inThemeOrange, .gt-text-orange { color: {$theme->secondary_color} !important; }\n";
        $css_content .= ".gt-text-blue { color: {$theme->accent_color} !important; }\n\n";
        
        $css_content .= "/* Background Colors */\n";
        $css_content .= ".gt-bg-green { background: {$theme->primary_color} !important; }\n";
        $css_content .= ".gt-bg-orange { background: {$theme->secondary_color} !important; }\n";
        $css_content .= ".gt-bg-blue { background: {$theme->accent_color} !important; }\n\n";
        
        $css_content .= "/* Button Colors */\n";
        $css_content .= ".btn.gt-btn-green, .gt-btn-green { background-color: {$theme->button_primary_color} !important; border-color: {$theme->button_primary_color} !important; }\n";
        $css_content .= ".btn.gt-btn-orange, .gt-btn-orange { background-color: {$theme->button_secondary_color} !important; border-color: {$theme->button_secondary_color} !important; }\n";
        $css_content .= ".btn.gt-btn-green:hover { background-color: " . adjustBrightness($theme->button_primary_color, -20) . " !important; }\n";
        $css_content .= ".btn.gt-btn-orange:hover { background-color: " . adjustBrightness($theme->button_secondary_color, -20) . " !important; }\n\n";
        
        $css_content .= "/* Link Colors */\n";
        $css_content .= "a { color: {$theme->link_color}; }\n";
        $css_content .= "a:hover, a:focus { color: {$theme->link_hover_color}; }\n\n";
        
        $css_content .= "/* Header and Footer */\n";
        $css_content .= ".gt-header { background-color: {$theme->header_bg_color} !important; }\n";
        $css_content .= ".gt-footer { background-color: {$theme->footer_bg_color} !important; }\n\n";
        
        $css_content .= "/* Theme Meta Color */\n";
        $css_content .= "meta[name='theme-color'] { content: '{$theme->primary_color}'; }\n";
        
        // Save to CSS file
        file_put_contents('../css/theme-custom.css', $css_content);
    }
}

// Function to adjust color brightness
function adjustBrightness($hex, $steps) {
    $steps = max(-255, min(255, $steps));
    $hex = str_replace('#', '', $hex);
    
    if (strlen($hex) == 3) {
        $hex = str_repeat(substr($hex,0,1), 2).str_repeat(substr($hex,1,1), 2).str_repeat(substr($hex,2,1), 2);
    }
    
    $color_parts = str_split($hex, 2);
    $return = '#';
    
    foreach ($color_parts as $color) {
        $color = hexdec($color);
        $color = max(0, min(255, $color + $steps));
        $return .= str_pad(dechex($color), 2, '0', STR_PAD_LEFT);
    }
    
    return $return;
}

// Get current settings
$sql = $DatabaseCo->dbLink->query("SELECT * FROM theme_settings WHERE id='1'");
$data = mysqli_fetch_object($sql);
if (!$data) {
    // Create default record
    $DatabaseCo->dbLink->query("INSERT INTO theme_settings (id) VALUES (1)");
    $sql = $DatabaseCo->dbLink->query("SELECT * FROM theme_settings WHERE id='1'");
    $data = mysqli_fetch_object($sql);
}
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Admin | Theme Settings</title>
    <meta content='width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no' name='viewport'>
    
    <!-- Bootstrap & custom css -->
    <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="css/custom.css" rel="stylesheet" type="text/css" />

    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/48403ccd1a.js" crossorigin="anonymous"></script>
    
    <!-- Ionicons -->
    <link href="http://code.ionicframework.com/ionicons/2.0.0/css/ionicons.min.css" rel="stylesheet" type="text/css" />
    
    <!-- Theme css -->
    <link href="dist/css/AdminLTE.min.css" rel="stylesheet" type="text/css" />
    <link href="dist/css/skins/_all-skins.min.css" rel="stylesheet" type="text/css" />
    
    <!-- Checkbox css -->
    <link href="plugins/iCheck/square/blue.css" rel="stylesheet" type="text/css" />
    
    <!-- Post Validation CSS -->
    <link href="css/postvalidationcss.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="../css/validate.css">
    
    <style>
        .color-preview {
            width: 50px;
            height: 30px;
            border: 1px solid #ddd;
            border-radius: 3px;
            display: inline-block;
            margin-left: 10px;
            vertical-align: middle;
        }
        .color-group {
            margin-bottom: 15px;
        }
        .color-section {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>

<body class="skin-blue">
    <!-- Icon Loader -->
    <div class="preloader-wrapper text-center">
        <div class="spinner"></div>
    </div>
    <!-- /. Icon Loader-->
    <div class="wrapper" style="display:none" id="body">
        <!-- Header & Menu -->
        <?php include "page-part/header.php"; ?> 
        <?php include "page-part/left_panel.php"; ?>
        <!-- /. Header & Menu -->
        <div class="content-wrapper">
            <section class="content-header">
                <h1 class="lightGrey">Theme & Color Settings</h1>
                <ol class="breadcrumb">
                    <li><a href="dashboard"><i class="fa fa-home"></i> Home</a></li>
                    <li class="active">Theme Settings</li>
                </ol>
            </section>
            <section class="content">
                <div class="row">
                    <div class="box-body gtSiteChangeId">
                        <div class="box box-success">
                            <div class="box-body">
                                <?php if($msg != ""): ?>
                                    <div class="alert alert-success alert-dismissible">
                                        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                                        <h4><i class="icon fa fa-check"></i> Success!</h4>
                                        <?php echo $msg; ?>
                                    </div>
                                <?php endif; ?>
                                
                                <form action="" method="post" enctype="multipart/form-data">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <h3><i class="fa fa-palette"></i> Customize Website Colors</h3>
                                            <p class="text-muted">Customize the color scheme of your website. Changes will be applied immediately after saving.</p>
                                            <hr>
                                            
                                            <!-- Primary Colors Section -->
                                            <div class="color-section">
                                                <h4><i class="fa fa-star"></i> Primary Colors</h4>
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="color-group">
                                                            <label>Primary Color (Green Theme)</label>
                                                            <div class="input-group">
                                                                <input type="color" name="primary_color" class="form-control" value="<?php echo $data->primary_color; ?>" onchange="updatePreview(this, 'primary_preview')">
                                                                <span class="input-group-addon">
                                                                    <div class="color-preview" id="primary_preview" style="background-color: <?php echo $data->primary_color; ?>"></div>
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="color-group">
                                                            <label>Secondary Color (Orange Theme)</label>
                                                            <div class="input-group">
                                                                <input type="color" name="secondary_color" class="form-control" value="<?php echo $data->secondary_color; ?>" onchange="updatePreview(this, 'secondary_preview')">
                                                                <span class="input-group-addon">
                                                                    <div class="color-preview" id="secondary_preview" style="background-color: <?php echo $data->secondary_color; ?>"></div>
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="color-group">
                                                            <label>Accent Color (Blue Theme)</label>
                                                            <div class="input-group">
                                                                <input type="color" name="accent_color" class="form-control" value="<?php echo $data->accent_color; ?>" onchange="updatePreview(this, 'accent_preview')">
                                                                <span class="input-group-addon">
                                                                    <div class="color-preview" id="accent_preview" style="background-color: <?php echo $data->accent_color; ?>"></div>
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- Status Colors Section -->
                                            <div class="color-section">
                                                <h4><i class="fa fa-info-circle"></i> Status Colors</h4>
                                                <div class="row">
                                                    <div class="col-md-3">
                                                        <div class="color-group">
                                                            <label>Success Color</label>
                                                            <div class="input-group">
                                                                <input type="color" name="success_color" class="form-control" value="<?php echo $data->success_color; ?>">
                                                                <span class="input-group-addon">
                                                                    <div class="color-preview" style="background-color: <?php echo $data->success_color; ?>"></div>
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="color-group">
                                                            <label>Warning Color</label>
                                                            <div class="input-group">
                                                                <input type="color" name="warning_color" class="form-control" value="<?php echo $data->warning_color; ?>">
                                                                <span class="input-group-addon">
                                                                    <div class="color-preview" style="background-color: <?php echo $data->warning_color; ?>"></div>
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="color-group">
                                                            <label>Danger Color</label>
                                                            <div class="input-group">
                                                                <input type="color" name="danger_color" class="form-control" value="<?php echo $data->danger_color; ?>">
                                                                <span class="input-group-addon">
                                                                    <div class="color-preview" style="background-color: <?php echo $data->danger_color; ?>"></div>
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="color-group">
                                                            <label>Dark Color</label>
                                                            <div class="input-group">
                                                                <input type="color" name="dark_color" class="form-control" value="<?php echo $data->dark_color; ?>">
                                                                <span class="input-group-addon">
                                                                    <div class="color-preview" style="background-color: <?php echo $data->dark_color; ?>"></div>
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- Button Colors Section -->
                                            <div class="color-section">
                                                <h4><i class="fa fa-mouse-pointer"></i> Button & Link Colors</h4>
                                                <div class="row">
                                                    <div class="col-md-3">
                                                        <div class="color-group">
                                                            <label>Primary Button</label>
                                                            <div class="input-group">
                                                                <input type="color" name="button_primary_color" class="form-control" value="<?php echo $data->button_primary_color; ?>">
                                                                <span class="input-group-addon">
                                                                    <div class="color-preview" style="background-color: <?php echo $data->button_primary_color; ?>"></div>
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="color-group">
                                                            <label>Secondary Button</label>
                                                            <div class="input-group">
                                                                <input type="color" name="button_secondary_color" class="form-control" value="<?php echo $data->button_secondary_color; ?>">
                                                                <span class="input-group-addon">
                                                                    <div class="color-preview" style="background-color: <?php echo $data->button_secondary_color; ?>"></div>
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="color-group">
                                                            <label>Link Color</label>
                                                            <div class="input-group">
                                                                <input type="color" name="link_color" class="form-control" value="<?php echo $data->link_color; ?>">
                                                                <span class="input-group-addon">
                                                                    <div class="color-preview" style="background-color: <?php echo $data->link_color; ?>"></div>
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="color-group">
                                                            <label>Link Hover Color</label>
                                                            <div class="input-group">
                                                                <input type="color" name="link_hover_color" class="form-control" value="<?php echo $data->link_hover_color; ?>">
                                                                <span class="input-group-addon">
                                                                    <div class="color-preview" style="background-color: <?php echo $data->link_hover_color; ?>"></div>
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- Layout Colors Section -->
                                            <div class="color-section">
                                                <h4><i class="fa fa-layout"></i> Layout Colors</h4>
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="color-group">
                                                            <label>Header Background</label>
                                                            <div class="input-group">
                                                                <input type="color" name="header_bg_color" class="form-control" value="<?php echo $data->header_bg_color; ?>">
                                                                <span class="input-group-addon">
                                                                    <div class="color-preview" style="background-color: <?php echo $data->header_bg_color; ?>"></div>
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="color-group">
                                                            <label>Footer Background</label>
                                                            <div class="input-group">
                                                                <input type="color" name="footer_bg_color" class="form-control" value="<?php echo $data->footer_bg_color; ?>">
                                                                <span class="input-group-addon">
                                                                    <div class="color-preview" style="background-color: <?php echo $data->footer_bg_color; ?>"></div>
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="color-group">
                                                            <label>Light Background</label>
                                                            <div class="input-group">
                                                                <input type="color" name="light_color" class="form-control" value="<?php echo $data->light_color; ?>">
                                                                <span class="input-group-addon">
                                                                    <div class="color-preview" style="background-color: <?php echo $data->light_color; ?>"></div>
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="form-group">
                                                <button type="submit" name="update_theme_settings" class="btn btn-success btn-lg">
                                                    <i class="fa fa-save"></i> Save Theme Settings
                                                </button>
                                                <button type="submit" name="reset_theme" class="btn btn-warning btn-lg" onclick="return confirm('Are you sure you want to reset to default colors?')">
                                                    <i class="fa fa-undo"></i> Reset to Default
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
        <?php include "page-part/footer.php"; ?>
    </div>

    <!-- jQuery -->
    <script src="plugins/jQuery/jQuery-2.1.3.min.js"></script>
    <!-- Bootstrap -->
    <script src="bootstrap/js/bootstrap.min.js" type="text/javascript"></script>
    <!-- AdminLTE App -->
    <script src="dist/js/app.min.js" type="text/javascript"></script>
    
    <script>
        $(document).ready(function() {
            $('#body').show();
            $('.preloader-wrapper').hide();
        });
        
        function updatePreview(input, previewId) {
            document.getElementById(previewId).style.backgroundColor = input.value;
        }
    </script>
</body>
</html>
