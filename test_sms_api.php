<?php
// Test script to check Fast2SMS API
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Test parameters
$test_mobile = "9999999999"; // Replace with a test mobile number
$test_otp = "1234";

// API URL from your mobile-apis.php
$authorization = "r2oeVHLQzpadwlNAGiEXtBfguK1IWS7YxZ58ROqn3DhMFvy9UbjN4C6iqf59nFPW8yD0wuZslmAJRtOr";
$url = "https://www.fast2sms.com/dev/bulkV2?authorization=$authorization&route=otp&variables_values=$test_otp&flash=0&numbers=$test_mobile";

echo "Testing Fast2SMS API...\n";
echo "URL: $url\n\n";

// Test using file() function (as used in your code)
echo "Testing with file() function:\n";
$ret = file($url);
if ($ret === false) {
    echo "ERROR: file() function failed\n";
} else {
    echo "Response: " . print_r($ret, true) . "\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test using cURL for better error handling
echo "Testing with cURL:\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP Code: $http_code\n";
if ($error) {
    echo "cURL Error: $error\n";
}
echo "Response: $response\n";

echo "\n" . str_repeat("-", 50) . "\n\n";

// Check if allow_url_fopen is enabled
echo "PHP Configuration Check:\n";
echo "allow_url_fopen: " . (ini_get('allow_url_fopen') ? 'Enabled' : 'Disabled') . "\n";
echo "cURL extension: " . (extension_loaded('curl') ? 'Available' : 'Not Available') . "\n";
echo "OpenSSL extension: " . (extension_loaded('openssl') ? 'Available' : 'Not Available') . "\n";

?>
