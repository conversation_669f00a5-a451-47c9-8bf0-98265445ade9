<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>Register Now - Modern Matrimony</title>
    
    <!-- CSS Files -->
    <link href="css/bootstrap.css" rel="stylesheet">
    <link href="css/custom-responsive.css" rel="stylesheet">
    <link href="css/custom.css" rel="stylesheet">
    <link href="css/icon-fixes.css" rel="stylesheet">
    <link href="css/mobile-responsive-fixes.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/48403ccd1a.js" crossorigin="anonymous"></script>
    
    <!-- Icon Loader -->
    <script src="js/icon-loader.js"></script>
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .container {
            padding: 20px 15px;
        }
        
        /* Additional modern styles */
        .form-step {
            display: none;
        }
        
        .form-step.active {
            display: block;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
            color: #666;
            position: relative;
        }
        
        .step.active {
            background: #ff6b35;
            color: white;
        }
        
        .step.completed {
            background: #22c55e;
            color: white;
        }
        
        .step::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 100%;
            width: 60px;
            height: 2px;
            background: #e0e0e0;
            transform: translateY(-50%);
        }
        
        .step:last-child::after {
            display: none;
        }
        
        .step.completed::after {
            background: #22c55e;
        }
        
        .navigation-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
        }
        
        .btn-prev, .btn-next {
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-prev {
            background: #6b7280;
            color: white;
        }
        
        .btn-next {
            background: #ff6b35;
            color: white;
        }
        
        .btn-prev:hover, .btn-next:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="modern-register-card">
            <div class="modern-register-form">
                <h1 class="modern-register-title">Register Now</h1>
                
                <!-- Step Indicator -->
                <div class="step-indicator">
                    <div class="step active" id="step-1">1</div>
                    <div class="step" id="step-2">2</div>
                    <div class="step" id="step-3">3</div>
                </div>
                
                <form id="modernRegisterForm" method="post" action="">
                    
                    <!-- Step 1: Basic Information -->
                    <div class="form-step active" id="form-step-1">
                        <h3 style="text-align: center; color: #374151; margin-bottom: 25px;">Basic Information</h3>
                        
                        <!-- Profile Created By -->
                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <span class="required">*</span>Profile Created By
                            </label>
                            <i class="fa fa-users form-icon"></i>
                            <select class="gt-form-control" name="profile_by" required>
                                <option value="">Profile Created By</option>
                                <option value="Self">Self</option>
                                <option value="Parents">Parents</option>
                                <option value="Guardian">Guardian</option>
                                <option value="Relative">Relative</option>
                                <option value="Friend">Friend</option>
                            </select>
                        </div>
                        
                        <!-- Gender Selection -->
                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <span class="required">*</span>Select Gender
                            </label>
                            <div class="gender-selection">
                                <div class="gender-option">
                                    <input type="radio" id="male" name="gender" value="Male" required>
                                    <label for="male">
                                        <i class="fa fa-mars"></i> Male
                                    </label>
                                </div>
                                <div class="gender-option">
                                    <input type="radio" id="female" name="gender" value="Female" required>
                                    <label for="female">
                                        <i class="fa fa-venus"></i> Female
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <!-- First Name -->
                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <span class="required">*</span>First Name
                            </label>
                            <i class="fa fa-user form-icon"></i>
                            <input type="text" class="gt-form-control" name="first_name" placeholder="Enter First Name" required>
                        </div>
                        
                        <!-- Last Name -->
                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <span class="required">*</span>Last Name
                            </label>
                            <i class="fa fa-user form-icon"></i>
                            <input type="text" class="gt-form-control" name="last_name" placeholder="Enter Last Name" required>
                        </div>
                        
                        <!-- Date of Birth -->
                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <span class="required">*</span>Date of Birth
                            </label>
                            <div class="date-picker-row">
                                <div class="modern-form-group">
                                    <i class="fa fa-calendar form-icon"></i>
                                    <select class="gt-form-control" name="birth_day" required>
                                        <option value="">01</option>
                                        <?php for($i = 1; $i <= 31; $i++): ?>
                                            <option value="<?php echo sprintf('%02d', $i); ?>"><?php echo sprintf('%02d', $i); ?></option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                                <div class="modern-form-group">
                                    <i class="fa fa-calendar form-icon"></i>
                                    <select class="gt-form-control" name="birth_month" required>
                                        <option value="">Month</option>
                                        <option value="01">January</option>
                                        <option value="02">February</option>
                                        <option value="03">March</option>
                                        <option value="04">April</option>
                                        <option value="05">May</option>
                                        <option value="06">June</option>
                                        <option value="07">July</option>
                                        <option value="08">August</option>
                                        <option value="09">September</option>
                                        <option value="10">October</option>
                                        <option value="11">November</option>
                                        <option value="12">December</option>
                                    </select>
                                </div>
                                <div class="modern-form-group">
                                    <i class="fa fa-calendar form-icon"></i>
                                    <select class="gt-form-control" name="birth_year" required>
                                        <option value="">Year</option>
                                        <?php 
                                        $currentYear = date('Y');
                                        for($i = $currentYear - 18; $i >= $currentYear - 60; $i--): 
                                        ?>
                                            <option value="<?php echo $i; ?>"><?php echo $i; ?></option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Religion -->
                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <span class="required">*</span>Select Your Religion
                            </label>
                            <i class="fa fa-book form-icon"></i>
                            <select class="gt-form-control" name="religion" required>
                                <option value="">Select Your Religion</option>
                                <option value="Hindu">Hindu</option>
                                <option value="Muslim">Muslim</option>
                                <option value="Christian">Christian</option>
                                <option value="Sikh">Sikh</option>
                                <option value="Buddhist">Buddhist</option>
                                <option value="Jain">Jain</option>
                                <option value="Parsi">Parsi</option>
                                <option value="Jewish">Jewish</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>
                        
                        <!-- Religion First -->
                        <div class="modern-form-group">
                            <label class="modern-form-label">Select Religion First</label>
                            <i class="fa fa-users form-icon"></i>
                            <select class="gt-form-control" name="religion_first">
                                <option value="">Select Religion First</option>
                                <option value="Yes">Yes</option>
                                <option value="No">No</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- Step 2: Contact Information -->
                    <div class="form-step" id="form-step-2">
                        <h3 style="text-align: center; color: #374151; margin-bottom: 25px;">Contact Information</h3>
                        
                        <!-- Mother Tongue -->
                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <span class="required">*</span>Mother Tongue
                            </label>
                            <i class="fa fa-globe form-icon"></i>
                            <select class="gt-form-control" name="mother_tongue" required>
                                <option value="">Mother Tongue</option>
                                <option value="Hindi">Hindi</option>
                                <option value="English">English</option>
                                <option value="Bengali">Bengali</option>
                                <option value="Telugu">Telugu</option>
                                <option value="Marathi">Marathi</option>
                                <option value="Tamil">Tamil</option>
                                <option value="Gujarati">Gujarati</option>
                                <option value="Urdu">Urdu</option>
                                <option value="Kannada">Kannada</option>
                                <option value="Odia">Odia</option>
                                <option value="Malayalam">Malayalam</option>
                                <option value="Punjabi">Punjabi</option>
                            </select>
                        </div>
                        
                        <!-- Country -->
                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <span class="required">*</span>Country
                            </label>
                            <i class="fa fa-flag form-icon"></i>
                            <select class="gt-form-control" name="country" required>
                                <option value="">Country</option>
                                <option value="India">India</option>
                                <option value="USA">USA</option>
                                <option value="UK">UK</option>
                                <option value="Canada">Canada</option>
                                <option value="Australia">Australia</option>
                                <option value="UAE">UAE</option>
                                <option value="Singapore">Singapore</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>
                        
                        <!-- Phone Number -->
                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <span class="required">*</span>Mobile Number
                            </label>
                            <div class="phone-input-group">
                                <div class="country-code">
                                    <i class="fa fa-phone form-icon"></i>
                                    <select class="gt-form-control" name="country_code" required>
                                        <option value="+91">+91</option>
                                        <option value="+1">+1</option>
                                        <option value="+44">+44</option>
                                        <option value="+61">+61</option>
                                        <option value="+971">+971</option>
                                    </select>
                                </div>
                                <div class="phone-number">
                                    <i class="fa fa-mobile-alt form-icon"></i>
                                    <input type="tel" class="gt-form-control" name="mobile" placeholder="Enter Your 10 Digit No" required maxlength="10">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Email -->
                        <div class="modern-form-group">
                            <label class="modern-form-label">
                                <span class="required">*</span>Email Address
                            </label>
                            <i class="fa fa-envelope form-icon"></i>
                            <input type="email" class="gt-form-control" name="email" placeholder="Enter Your Email Id" required>
                        </div>
                    </div>
                    
                    <!-- Step 3: Terms & Submit -->
                    <div class="form-step" id="form-step-3">
                        <h3 style="text-align: center; color: #374151; margin-bottom: 25px;">Complete Registration</h3>
                        
                        <div style="text-align: center; margin: 30px 0;">
                            <i class="fa fa-check-circle" style="font-size: 60px; color: #22c55e; margin-bottom: 20px;"></i>
                            <h4 style="color: #374151; margin-bottom: 15px;">Almost Done!</h4>
                            <p style="color: #6b7280;">Please review your information and accept our terms to complete registration.</p>
                        </div>
                        
                        <!-- Terms and Conditions -->
                        <div class="modern-checkbox">
                            <input type="checkbox" id="terms" name="terms" required>
                            <label for="terms">
                                I accept <a href="#" style="color: #ff6b35; text-decoration: none;">terms & conditions</a> and <a href="#" style="color: #ff6b35; text-decoration: none;">privacy policy</a>.
                            </label>
                        </div>
                        
                        <!-- Submit Button -->
                        <button type="submit" class="modern-submit-btn">
                            <i class="fa fa-user-plus" style="margin-right: 10px;"></i>
                            Register Now
                        </button>
                    </div>
                    
                    <!-- Navigation Buttons -->
                    <div class="navigation-buttons">
                        <button type="button" class="btn-prev" id="prevBtn" onclick="changeStep(-1)" style="display: none;">
                            <i class="fa fa-arrow-left"></i> Previous
                        </button>
                        <button type="button" class="btn-next" id="nextBtn" onclick="changeStep(1)">
                            Next <i class="fa fa-arrow-right"></i>
                        </button>
                    </div>
                </form>
                
                <!-- Login Link -->
                <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0;">
                    <p style="color: #6b7280; margin: 0;">
                        Already have an account? 
                        <a href="login.php" style="color: #ff6b35; text-decoration: none; font-weight: 600;">Login here</a>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/jquery.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    
    <script>
        let currentStep = 1;
        const totalSteps = 3;
        
        function showStep(step) {
            // Hide all steps
            document.querySelectorAll('.form-step').forEach(s => s.classList.remove('active'));
            document.querySelectorAll('.step').forEach(s => s.classList.remove('active'));
            
            // Show current step
            document.getElementById(`form-step-${step}`).classList.add('active');
            document.getElementById(`step-${step}`).classList.add('active');
            
            // Mark completed steps
            for(let i = 1; i < step; i++) {
                document.getElementById(`step-${i}`).classList.add('completed');
            }
            
            // Update navigation buttons
            document.getElementById('prevBtn').style.display = step === 1 ? 'none' : 'inline-block';
            document.getElementById('nextBtn').style.display = step === totalSteps ? 'none' : 'inline-block';
        }
        
        function changeStep(direction) {
            const newStep = currentStep + direction;
            
            if (newStep >= 1 && newStep <= totalSteps) {
                // Validate current step before proceeding
                if (direction === 1 && !validateStep(currentStep)) {
                    return;
                }
                
                currentStep = newStep;
                showStep(currentStep);
            }
        }
        
        function validateStep(step) {
            const currentStepElement = document.getElementById(`form-step-${step}`);
            const requiredFields = currentStepElement.querySelectorAll('[required]');
            
            for (let field of requiredFields) {
                if (!field.value.trim()) {
                    field.focus();
                    alert('Please fill in all required fields.');
                    return false;
                }
            }
            
            return true;
        }
        
        // Form submission
        document.getElementById('modernRegisterForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (!validateStep(totalSteps)) {
                return;
            }
            
            // Show loading
            const submitBtn = document.querySelector('.modern-submit-btn');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Creating Account...';
            submitBtn.disabled = true;
            
            // Simulate form submission
            setTimeout(() => {
                alert('Registration successful! Welcome to our matrimony platform.');
                // Redirect to login or dashboard
                // window.location.href = 'login.php';
                
                // Reset for demo
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 2000);
        });
        
        // Initialize
        showStep(1);
        
        console.log('🎨 Modern Registration Form Loaded');
        console.log('📱 Multi-step form with styled icons');
    </script>
</body>
</html>
