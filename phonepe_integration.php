<?php
/**
 * PhonePe Payment Gateway Integration
 * Documentation: https://developer.phonepe.com/v1/docs/introduction-pg
 */

class PhonePePayment {
    
    private $merchant_id;
    private $salt_key;
    private $salt_index;
    private $base_url;
    private $redirect_url;
    private $callback_url;
    
    public function __construct($merchant_id, $salt_key, $salt_index = 1, $is_production = false) {
        $this->merchant_id = $merchant_id;
        $this->salt_key = $salt_key;
        $this->salt_index = $salt_index;
        
        // Set URLs based on environment
        if ($is_production) {
            $this->base_url = 'https://api.phonepe.com/apis/hermes/pg/v1/pay';
        } else {
            $this->base_url = 'https://api-preprod.phonepe.com/apis/hermes/pg/v1/pay';
        }
        
        $this->redirect_url = $this->getCurrentDomain() . '/phonepe_callback.php';
        $this->callback_url = $this->getCurrentDomain() . '/phonepe_webhook.php';
    }
    
    private function getCurrentDomain() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        return $protocol . '://' . $host;
    }
    
    /**
     * Create payment request
     */
    public function createPayment($order_id, $amount, $user_data) {
        // Convert amount to paise (multiply by 100)
        $amount_in_paise = $amount * 100;
        
        // Create payment payload
        $payload = [
            'merchantId' => $this->merchant_id,
            'merchantTransactionId' => $order_id,
            'merchantUserId' => 'MUID' . $user_data['user_id'],
            'amount' => $amount_in_paise,
            'redirectUrl' => $this->redirect_url,
            'redirectMode' => 'POST',
            'callbackUrl' => $this->callback_url,
            'mobileNumber' => $user_data['mobile'],
            'paymentInstrument' => [
                'type' => 'PAY_PAGE'
            ]
        ];
        
        // Encode payload to base64
        $encoded_payload = base64_encode(json_encode($payload));
        
        // Create checksum
        $checksum = $this->generateChecksum($encoded_payload);
        
        // Prepare request data
        $request_data = [
            'request' => $encoded_payload
        ];
        
        // Headers
        $headers = [
            'Content-Type: application/json',
            'X-VERIFY: ' . $checksum,
            'accept: application/json'
        ];
        
        // Make API call
        $response = $this->makeApiCall($this->base_url, $request_data, $headers);
        
        return $response;
    }
    
    /**
     * Generate checksum for PhonePe API
     */
    private function generateChecksum($payload) {
        $string = $payload . '/pg/v1/pay' . $this->salt_key;
        $checksum = hash('sha256', $string) . '###' . $this->salt_index;
        return $checksum;
    }
    
    /**
     * Verify payment status
     */
    public function verifyPayment($merchant_transaction_id) {
        $url = str_replace('/pay', '/status/' . $this->merchant_id . '/' . $merchant_transaction_id, $this->base_url);
        
        // Create checksum for status check
        $string = '/pg/v1/status/' . $this->merchant_id . '/' . $merchant_transaction_id . $this->salt_key;
        $checksum = hash('sha256', $string) . '###' . $this->salt_index;
        
        $headers = [
            'Content-Type: application/json',
            'X-VERIFY: ' . $checksum,
            'X-MERCHANT-ID: ' . $this->merchant_id,
            'accept: application/json'
        ];
        
        $response = $this->makeApiCall($url, null, $headers, 'GET');
        
        return $response;
    }
    
    /**
     * Make cURL API call
     */
    private function makeApiCall($url, $data = null, $headers = [], $method = 'POST') {
        $curl = curl_init();
        
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_SSL_VERIFYPEER => false
        ]);
        
        if ($method === 'POST' && $data) {
            curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($curl);
        $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $error = curl_error($curl);
        
        curl_close($curl);
        
        if ($error) {
            return [
                'success' => false,
                'error' => $error,
                'http_code' => $http_code
            ];
        }
        
        $decoded_response = json_decode($response, true);
        
        return [
            'success' => true,
            'data' => $decoded_response,
            'http_code' => $http_code,
            'raw_response' => $response
        ];
    }
    
    /**
     * Validate webhook signature
     */
    public function validateWebhookSignature($payload, $signature) {
        $expected_signature = hash('sha256', $payload . $this->salt_key) . '###' . $this->salt_index;
        return hash_equals($expected_signature, $signature);
    }
    
    /**
     * Process refund
     */
    public function processRefund($original_transaction_id, $refund_amount, $refund_id) {
        $refund_url = str_replace('/pay', '/refund', $this->base_url);
        
        $amount_in_paise = $refund_amount * 100;
        
        $payload = [
            'merchantId' => $this->merchant_id,
            'merchantUserId' => 'MUID_REFUND',
            'originalTransactionId' => $original_transaction_id,
            'merchantTransactionId' => $refund_id,
            'amount' => $amount_in_paise,
            'callbackUrl' => $this->callback_url
        ];
        
        $encoded_payload = base64_encode(json_encode($payload));
        $checksum = hash('sha256', $encoded_payload . '/pg/v1/refund' . $this->salt_key) . '###' . $this->salt_index;
        
        $request_data = ['request' => $encoded_payload];
        
        $headers = [
            'Content-Type: application/json',
            'X-VERIFY: ' . $checksum,
            'accept: application/json'
        ];
        
        return $this->makeApiCall($refund_url, $request_data, $headers);
    }
}

/**
 * PhonePe Configuration Class
 */
class PhonePeConfig {
    
    public static function getConfig($database_connection) {
        $result = $database_connection->query("SELECT * FROM payment_method WHERE pay_id='4'");
        
        if ($result && mysqli_num_rows($result) > 0) {
            return mysqli_fetch_object($result);
        }
        
        return null;
    }
    
    public static function updateConfig($database_connection, $config_data) {
        $merchant_id = mysqli_real_escape_string($database_connection, $config_data['merchant_id']);
        $salt_key = mysqli_real_escape_string($database_connection, $config_data['salt_key']);
        $salt_index = mysqli_real_escape_string($database_connection, $config_data['salt_index']);
        $is_production = mysqli_real_escape_string($database_connection, $config_data['is_production']);
        $status = mysqli_real_escape_string($database_connection, $config_data['status']);
        
        // Check if PhonePe config exists
        $check = $database_connection->query("SELECT pay_id FROM payment_method WHERE pay_id='4'");
        
        if (mysqli_num_rows($check) > 0) {
            // Update existing config
            $sql = "UPDATE payment_method SET 
                    phonepe_merchant_id='$merchant_id',
                    phonepe_salt_key='$salt_key',
                    phonepe_salt_index='$salt_index',
                    phonepe_is_production='$is_production',
                    status='$status'
                    WHERE pay_id='4'";
        } else {
            // Insert new config
            $sql = "INSERT INTO payment_method (
                    pay_id, pay_name, phonepe_merchant_id, phonepe_salt_key, 
                    phonepe_salt_index, phonepe_is_production, status
                    ) VALUES (
                    '4', 'PhonePe', '$merchant_id', '$salt_key', 
                    '$salt_index', '$is_production', '$status'
                    )";
        }
        
        return $database_connection->query($sql);
    }
}

/**
 * Utility functions for PhonePe integration
 */
class PhonePeUtils {
    
    /**
     * Generate unique transaction ID
     */
    public static function generateTransactionId($prefix = 'TXN') {
        return $prefix . '_' . time() . '_' . rand(1000, 9999);
    }
    
    /**
     * Log transaction for debugging
     */
    public static function logTransaction($transaction_id, $data, $type = 'INFO') {
        $log_file = 'logs/phonepe_transactions.log';
        $log_entry = date('Y-m-d H:i:s') . " [$type] [$transaction_id] " . json_encode($data) . PHP_EOL;
        
        // Create logs directory if it doesn't exist
        if (!file_exists('logs')) {
            mkdir('logs', 0755, true);
        }
        
        file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Format amount for display
     */
    public static function formatAmount($amount, $currency = '₹') {
        return $currency . ' ' . number_format($amount, 2);
    }
    
    /**
     * Validate mobile number
     */
    public static function validateMobile($mobile) {
        // Remove any non-digit characters
        $mobile = preg_replace('/[^0-9]/', '', $mobile);
        
        // Check if it's a valid Indian mobile number
        if (strlen($mobile) == 10 && preg_match('/^[6-9]/', $mobile)) {
            return $mobile;
        } elseif (strlen($mobile) == 12 && substr($mobile, 0, 2) == '91') {
            return substr($mobile, 2);
        }
        
        return false;
    }
    
    /**
     * Get payment status message
     */
    public static function getStatusMessage($status_code) {
        $status_messages = [
            'PAYMENT_SUCCESS' => 'Payment completed successfully',
            'PAYMENT_ERROR' => 'Payment failed due to error',
            'PAYMENT_PENDING' => 'Payment is pending',
            'PAYMENT_DECLINED' => 'Payment was declined',
            'INTERNAL_SERVER_ERROR' => 'Internal server error occurred',
            'TRANSACTION_NOT_FOUND' => 'Transaction not found',
            'BAD_REQUEST' => 'Invalid request parameters',
            'AUTHORIZATION_FAILED' => 'Authorization failed',
            'TIMED_OUT' => 'Transaction timed out'
        ];
        
        return isset($status_messages[$status_code]) ? $status_messages[$status_code] : 'Unknown status';
    }
}
?>
