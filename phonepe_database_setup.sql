-- PhonePe Payment Gateway Database Setup
-- Add PhonePe configuration to payment_method table

-- First, check if PhonePe entry exists
SELECT * FROM payment_method WHERE pay_id = '4';

-- If not exists, insert PhonePe payment method
INSERT IGNORE INTO payment_method (
    pay_id, 
    pay_name, 
    phonepe_merchant_id, 
    phonepe_salt_key, 
    phonepe_salt_index, 
    phonepe_is_production, 
    status
) VALUES (
    '4', 
    'PhonePe', 
    '', 
    '', 
    '1', 
    'NO', 
    'PENDING'
);

-- Add PhonePe specific columns if they don't exist
ALTER TABLE payment_method 
ADD COLUMN IF NOT EXISTS phonepe_merchant_id VARCHAR(255) DEFAULT '',
ADD COLUMN IF NOT EXISTS phonepe_salt_key TEXT DEFAULT '',
ADD COLUMN IF NOT EXISTS phonepe_salt_index INT DEFAULT 1,
ADD COLUMN IF NOT EXISTS phonepe_is_production ENUM('YES', 'NO') DEFAULT 'NO';

-- Update payments table to support longer transaction IDs
ALTER TABLE payments 
MODIFY COLUMN pay_id VARCHAR(100);

-- Add updated_at column to payments table for webhook tracking
ALTER TABLE payments 
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- Create PhonePe transaction log table for debugging
CREATE TABLE IF NOT EXISTS phonepe_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    transaction_id VARCHAR(100) NOT NULL,
    merchant_transaction_id VARCHAR(100) NOT NULL,
    user_id VARCHAR(50),
    plan_id VARCHAR(20),
    amount DECIMAL(10,2),
    status VARCHAR(50),
    response_code VARCHAR(50),
    payment_method VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    raw_response TEXT,
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_merchant_transaction_id (merchant_transaction_id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status)
);

-- Sample data for testing (optional)
-- UPDATE payment_method SET 
--     phonepe_merchant_id = 'YOUR_MERCHANT_ID',
--     phonepe_salt_key = 'YOUR_SALT_KEY',
--     phonepe_salt_index = 1,
--     phonepe_is_production = 'NO',
--     status = 'APPROVED'
-- WHERE pay_id = '4';

-- Verify the setup
SELECT * FROM payment_method WHERE pay_id = '4';
DESCRIBE payment_method;
DESCRIBE payments;
DESCRIBE phonepe_transactions;
