<?php
/**
 * PhonePe Production Configuration Helper
 * Assists with switching from sandbox to production
 */

include_once 'databaseConn.php';
include_once 'phonepe_integration.php';
include_once 'lib/requestHandler.php';

$DatabaseCo = new DatabaseConn();

// Check admin session
if (!isset($_SESSION['admin_user_name'])) {
    echo "<script>window.location='premium_admin/index.php';</script>";
    exit;
}

$message = '';
$message_type = '';

// Handle production configuration
if (isset($_POST['configure_production'])) {
    try {
        $production_config = [
            'merchant_id' => mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['prod_merchant_id']),
            'salt_key' => mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['prod_salt_key']),
            'salt_index' => mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['prod_salt_index']),
            'is_production' => 'YES',
            'status' => 'APPROVED'
        ];
        
        // Validate production credentials
        if (empty($production_config['merchant_id']) || empty($production_config['salt_key'])) {
            throw new Exception('Production Merchant ID and Salt Key are required');
        }
        
        if (strlen($production_config['salt_key']) < 20) {
            throw new Exception('Salt Key appears to be invalid (too short)');
        }
        
        // Update configuration
        if (PhonePeConfig::updateConfig($DatabaseCo->dbLink, $production_config)) {
            $message = 'Production configuration updated successfully! PhonePe is now in LIVE mode.';
            $message_type = 'success';
            
            // Log the production switch
            PhonePeUtils::logTransaction('PRODUCTION_SWITCH', [
                'action' => 'switched_to_production',
                'admin_user' => $_SESSION['admin_user_name'],
                'timestamp' => date('Y-m-d H:i:s'),
                'merchant_id' => $production_config['merchant_id']
            ]);
        } else {
            throw new Exception('Failed to update production configuration');
        }
    } catch (Exception $e) {
        $message = 'Error: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// Handle rollback to sandbox
if (isset($_POST['rollback_sandbox'])) {
    try {
        $sandbox_config = [
            'merchant_id' => mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['sandbox_merchant_id']),
            'salt_key' => mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['sandbox_salt_key']),
            'salt_index' => '1',
            'is_production' => 'NO',
            'status' => 'APPROVED'
        ];
        
        if (PhonePeConfig::updateConfig($DatabaseCo->dbLink, $sandbox_config)) {
            $message = 'Rolled back to sandbox configuration successfully!';
            $message_type = 'warning';
            
            // Log the rollback
            PhonePeUtils::logTransaction('SANDBOX_ROLLBACK', [
                'action' => 'rolled_back_to_sandbox',
                'admin_user' => $_SESSION['admin_user_name'],
                'timestamp' => date('Y-m-d H:i:s')
            ]);
        } else {
            throw new Exception('Failed to rollback to sandbox');
        }
    } catch (Exception $e) {
        $message = 'Rollback Error: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// Get current configuration
$current_config = PhonePeConfig::getConfig($DatabaseCo->dbLink);

// Check system readiness
$readiness_checks = [
    'ssl_certificate' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
    'database_connection' => $DatabaseCo->dbLink ? true : false,
    'phonepe_config' => $current_config ? true : false,
    'webhook_files' => file_exists('phonepe_webhook.php') && file_exists('phonepe_callback.php'),
    'admin_session' => isset($_SESSION['admin_user_name'])
];

$readiness_score = array_sum($readiness_checks) / count($readiness_checks) * 100;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>PhonePe Production Configuration</title>
    
    <!-- CSS Files -->
    <link href="css/bootstrap.css" rel="stylesheet">
    <link href="css/custom.css" rel="stylesheet">
    <link href="css/icon-fixes.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/48403ccd1a.js" crossorigin="anonymous"></script>
    
    <style>
        body { background: #f8f9fa; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .config-container { max-width: 900px; margin: 30px auto; padding: 0 20px; }
        .config-header { background: linear-gradient(135deg, #5f27cd 0%, #7c4dff 100%); color: white; padding: 25px; border-radius: 10px 10px 0 0; }
        .config-content { background: white; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .alert { padding: 15px; border-radius: 6px; margin-bottom: 20px; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .alert-warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .readiness-section { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .readiness-item { display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid #e9ecef; }
        .readiness-item:last-child { border-bottom: none; }
        .status-ready { color: #28a745; font-weight: 600; }
        .status-not-ready { color: #dc3545; font-weight: 600; }
        .form-section { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .form-control { padding: 12px; border: 1px solid #ddd; border-radius: 6px; margin-bottom: 15px; }
        .btn-production { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; padding: 12px 25px; border-radius: 6px; font-weight: 600; }
        .btn-production:hover { transform: translateY(-1px); box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3); color: white; }
        .btn-rollback { background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: #212529; border: none; padding: 12px 25px; border-radius: 6px; font-weight: 600; }
        .btn-rollback:hover { transform: translateY(-1px); box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3); }
        .current-config { background: #e3f2fd; padding: 15px; border-radius: 6px; border-left: 4px solid #2196f3; }
        .warning-box { background: #fff3e0; padding: 15px; border-radius: 6px; border-left: 4px solid #ff9800; margin: 15px 0; }
        .progress-bar { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; margin: 15px 0; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #28a745, #20c997); transition: width 0.3s ease; }
    </style>
</head>
<body>
    <div class="config-container">
        <!-- Header -->
        <div class="config-header">
            <h1><i class="fa fa-rocket"></i> PhonePe Production Configuration</h1>
            <p>Switch from sandbox to production environment</p>
        </div>
        
        <div class="config-content">
            <!-- Messages -->
            <?php if ($message): ?>
            <div class="alert alert-<?php echo $message_type; ?>">
                <i class="fa fa-<?php echo $message_type == 'success' ? 'check-circle' : ($message_type == 'warning' ? 'exclamation-triangle' : 'times-circle'); ?>"></i>
                <?php echo htmlspecialchars($message); ?>
            </div>
            <?php endif; ?>
            
            <!-- System Readiness -->
            <div class="readiness-section">
                <h3><i class="fa fa-clipboard-check"></i> System Readiness Check</h3>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: <?php echo $readiness_score; ?>%;"></div>
                </div>
                <p class="text-center"><strong>Readiness Score: <?php echo round($readiness_score); ?>%</strong></p>
                
                <div class="readiness-item">
                    <span><i class="fa fa-lock"></i> SSL Certificate (HTTPS)</span>
                    <span class="<?php echo $readiness_checks['ssl_certificate'] ? 'status-ready' : 'status-not-ready'; ?>">
                        <?php echo $readiness_checks['ssl_certificate'] ? '✓ Ready' : '✗ Not Ready'; ?>
                    </span>
                </div>
                
                <div class="readiness-item">
                    <span><i class="fa fa-database"></i> Database Connection</span>
                    <span class="<?php echo $readiness_checks['database_connection'] ? 'status-ready' : 'status-not-ready'; ?>">
                        <?php echo $readiness_checks['database_connection'] ? '✓ Ready' : '✗ Not Ready'; ?>
                    </span>
                </div>
                
                <div class="readiness-item">
                    <span><i class="fa fa-cog"></i> PhonePe Configuration</span>
                    <span class="<?php echo $readiness_checks['phonepe_config'] ? 'status-ready' : 'status-not-ready'; ?>">
                        <?php echo $readiness_checks['phonepe_config'] ? '✓ Ready' : '✗ Not Ready'; ?>
                    </span>
                </div>
                
                <div class="readiness-item">
                    <span><i class="fa fa-webhook"></i> Webhook Files</span>
                    <span class="<?php echo $readiness_checks['webhook_files'] ? 'status-ready' : 'status-not-ready'; ?>">
                        <?php echo $readiness_checks['webhook_files'] ? '✓ Ready' : '✗ Not Ready'; ?>
                    </span>
                </div>
                
                <div class="readiness-item">
                    <span><i class="fa fa-user-shield"></i> Admin Session</span>
                    <span class="<?php echo $readiness_checks['admin_session'] ? 'status-ready' : 'status-not-ready'; ?>">
                        <?php echo $readiness_checks['admin_session'] ? '✓ Ready' : '✗ Not Ready'; ?>
                    </span>
                </div>
            </div>
            
            <!-- Current Configuration -->
            <div class="current-config">
                <h3><i class="fa fa-info-circle"></i> Current Configuration</h3>
                <?php if ($current_config): ?>
                <div class="row">
                    <div class="col-md-6">
                        <strong>Environment:</strong> 
                        <?php echo $current_config->phonepe_is_production == 'YES' ? 'Production (LIVE)' : 'Sandbox (Testing)'; ?>
                    </div>
                    <div class="col-md-6">
                        <strong>Status:</strong> <?php echo $current_config->status; ?>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <strong>Merchant ID:</strong> <?php echo substr($current_config->phonepe_merchant_id, 0, 8) . '...'; ?>
                    </div>
                    <div class="col-md-6">
                        <strong>Salt Index:</strong> <?php echo $current_config->phonepe_salt_index; ?>
                    </div>
                </div>
                <?php else: ?>
                <p class="text-danger">No PhonePe configuration found. Please configure PhonePe first.</p>
                <?php endif; ?>
            </div>
            
            <?php if ($current_config && $current_config->phonepe_is_production == 'NO'): ?>
            <!-- Production Configuration Form -->
            <div class="form-section">
                <h3><i class="fa fa-rocket"></i> Switch to Production</h3>
                
                <div class="warning-box">
                    <strong>⚠️ Important:</strong> Switching to production will enable live payments with real money. 
                    Ensure you have completed all testing and have valid production credentials from PhonePe.
                </div>
                
                <form method="POST" id="production-form">
                    <div class="row">
                        <div class="col-md-6">
                            <label for="prod_merchant_id">Production Merchant ID *</label>
                            <input type="text" class="form-control" id="prod_merchant_id" name="prod_merchant_id" 
                                   placeholder="Enter production merchant ID" required>
                        </div>
                        <div class="col-md-6">
                            <label for="prod_salt_index">Salt Index *</label>
                            <input type="number" class="form-control" id="prod_salt_index" name="prod_salt_index" 
                                   value="1" min="1" required>
                        </div>
                    </div>
                    
                    <label for="prod_salt_key">Production Salt Key *</label>
                    <input type="password" class="form-control" id="prod_salt_key" name="prod_salt_key" 
                           placeholder="Enter production salt key" required>
                    
                    <div class="warning-box">
                        <h4>Pre-Production Checklist:</h4>
                        <label><input type="checkbox" required> PhonePe merchant account approved for production</label><br>
                        <label><input type="checkbox" required> All sandbox testing completed successfully</label><br>
                        <label><input type="checkbox" required> SSL certificate is valid and active</label><br>
                        <label><input type="checkbox" required> Webhook URLs configured in PhonePe dashboard</label><br>
                        <label><input type="checkbox" required> Business KYC verification completed</label><br>
                        <label><input type="checkbox" required> System backup completed</label>
                    </div>
                    
                    <button type="submit" name="configure_production" class="btn btn-production">
                        <i class="fa fa-rocket"></i> Switch to Production
                    </button>
                </form>
            </div>
            
            <?php elseif ($current_config && $current_config->phonepe_is_production == 'YES'): ?>
            <!-- Production Active - Rollback Option -->
            <div class="form-section">
                <h3><i class="fa fa-check-circle text-success"></i> Production Mode Active</h3>
                
                <div class="alert alert-success">
                    <strong>✓ PhonePe is currently in PRODUCTION mode.</strong><br>
                    All payments are being processed with real money. Monitor transactions carefully.
                </div>
                
                <h4>Emergency Rollback to Sandbox</h4>
                <div class="warning-box">
                    <strong>⚠️ Use only in emergency:</strong> This will switch back to sandbox mode and disable live payments.
                </div>
                
                <form method="POST" id="rollback-form">
                    <div class="row">
                        <div class="col-md-6">
                            <label for="sandbox_merchant_id">Sandbox Merchant ID</label>
                            <input type="text" class="form-control" id="sandbox_merchant_id" name="sandbox_merchant_id" 
                                   placeholder="Sandbox merchant ID">
                        </div>
                        <div class="col-md-6">
                            <label for="sandbox_salt_key">Sandbox Salt Key</label>
                            <input type="password" class="form-control" id="sandbox_salt_key" name="sandbox_salt_key" 
                                   placeholder="Sandbox salt key">
                        </div>
                    </div>
                    
                    <button type="submit" name="rollback_sandbox" class="btn btn-rollback" 
                            onclick="return confirm('Are you sure you want to rollback to sandbox? This will disable live payments.')">
                        <i class="fa fa-undo"></i> Emergency Rollback to Sandbox
                    </button>
                </form>
            </div>
            <?php endif; ?>
            
            <!-- Webhook Configuration -->
            <div class="form-section">
                <h3><i class="fa fa-webhook"></i> Webhook Configuration</h3>
                <p>Configure these URLs in your PhonePe merchant dashboard:</p>
                
                <div class="row">
                    <div class="col-md-6">
                        <label>Redirect URL:</label>
                        <div class="form-control" style="background: #f8f9fa;">
                            https://<?php echo $_SERVER['HTTP_HOST']; ?>/phonepe_callback.php
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label>Webhook URL:</label>
                        <div class="form-control" style="background: #f8f9fa;">
                            https://<?php echo $_SERVER['HTTP_HOST']; ?>/phonepe_webhook.php
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Links -->
            <div class="text-center" style="margin-top: 30px;">
                <a href="phonepe_production_deployment.php" class="btn btn-info">
                    <i class="fa fa-list-check"></i> View Deployment Checklist
                </a>
                <a href="test_phonepe_integration.php" class="btn btn-success">
                    <i class="fa fa-test-tube"></i> Test Integration
                </a>
                <a href="premium_admin/PhonePeSettings.php" class="btn btn-warning">
                    <i class="fa fa-cog"></i> Advanced Settings
                </a>
                <a href="premium_admin/dashboard.php" class="btn btn-secondary">
                    <i class="fa fa-arrow-left"></i> Back to Admin
                </a>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/jquery.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script>
        $(document).ready(function() {
            // Form validation
            $('#production-form').on('submit', function(e) {
                var merchantId = $('#prod_merchant_id').val().trim();
                var saltKey = $('#prod_salt_key').val().trim();
                
                if (!merchantId || !saltKey) {
                    e.preventDefault();
                    alert('Please fill in all required fields');
                    return false;
                }
                
                if (saltKey.length < 20) {
                    e.preventDefault();
                    alert('Salt Key appears to be too short. Please verify your production credentials.');
                    return false;
                }
                
                if (!confirm('Are you sure you want to switch to PRODUCTION mode? This will enable live payments with real money.')) {
                    e.preventDefault();
                    return false;
                }
            });
            
            // Show/hide password
            $('<button type="button" class="btn btn-sm btn-outline-secondary" style="margin-left: 10px;">Show</button>')
                .insertAfter('#prod_salt_key')
                .on('click', function() {
                    var input = $('#prod_salt_key');
                    var type = input.attr('type') === 'password' ? 'text' : 'password';
                    input.attr('type', type);
                    $(this).text(type === 'password' ? 'Show' : 'Hide');
                });
        });
    </script>
</body>
</html>
