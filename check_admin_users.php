<?php
include 'databaseConn.php';
$db = new DatabaseConn();

echo "Checking admin users...\n";

$result = $db->dbLink->query('SELECT * FROM admin_users');
if($result) {
    echo 'Admin users found: ' . mysqli_num_rows($result) . "\n";
    while($row = mysqli_fetch_assoc($result)) {
        echo 'User: ' . $row['uname'] . ' Email: ' . $row['email'] . ' Role: ' . $row['role_id'] . "\n";
    }
} else {
    echo 'Error: ' . mysqli_error($db->dbLink) . "\n";
}

// Check if table exists
$tables = $db->dbLink->query("SHOW TABLES LIKE 'admin_users'");
if(mysqli_num_rows($tables) == 0) {
    echo "admin_users table does not exist!\n";
} else {
    echo "admin_users table exists.\n";
}
?>
