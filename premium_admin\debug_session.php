<?php
/**
 * Session Debug Script
 * This script helps debug session issues in the admin panel
 */

include_once '../databaseConn.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Session Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: green; background: #e8f5e8; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid green; }
        .error { color: red; background: #ffe8e8; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid red; }
        .warning { color: orange; background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid orange; }
        .info { color: blue; background: #e8f0ff; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid blue; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
        .btn { display: inline-block; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .btn:hover { background: #005a87; }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
    </style>
</head>
<body>";

echo "<div class='container'>";
echo "<h1>🔍 Session Debug Information</h1>";

// Check session status
echo "<div class='info'>";
echo "<h2>📊 Session Status</h2>";
echo "<strong>Session Status:</strong> " . session_status() . "<br>";
echo "<strong>Session ID:</strong> " . session_id() . "<br>";
echo "<strong>Session Name:</strong> " . session_name() . "<br>";
echo "<strong>Session Save Path:</strong> " . session_save_path() . "<br>";
echo "</div>";

// Check current session data
echo "<div class='info'>";
echo "<h2>📋 Current Session Data</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";
echo "</div>";

// Check if admin session exists
echo "<div class='info'>";
echo "<h2>🔐 Admin Authentication Status</h2>";
if (isset($_SESSION['admin_user_name'])) {
    echo "<div class='success'>✅ Admin session exists</div>";
    echo "<strong>Admin Username:</strong> " . $_SESSION['admin_user_name'] . "<br>";
    echo "<strong>Admin Email:</strong> " . (isset($_SESSION['admin_email']) ? $_SESSION['admin_email'] : 'Not set') . "<br>";
    echo "<strong>Admin User ID:</strong> " . (isset($_SESSION['admin_user_id']) ? $_SESSION['admin_user_id'] : 'Not set') . "<br>";
} else {
    echo "<div class='error'>❌ No admin session found</div>";
    echo "<p>This means you are not logged in or the session has expired.</p>";
}
echo "</div>";

// Check cookies
echo "<div class='info'>";
echo "<h2>🍪 Cookie Information</h2>";
echo "<pre>";
print_r($_COOKIE);
echo "</pre>";
echo "</div>";

// Check PHP configuration
echo "<div class='info'>";
echo "<h2>⚙️ PHP Session Configuration</h2>";
echo "<strong>session.auto_start:</strong> " . ini_get('session.auto_start') . "<br>";
echo "<strong>session.cookie_lifetime:</strong> " . ini_get('session.cookie_lifetime') . "<br>";
echo "<strong>session.gc_maxlifetime:</strong> " . ini_get('session.gc_maxlifetime') . "<br>";
echo "<strong>session.use_cookies:</strong> " . ini_get('session.use_cookies') . "<br>";
echo "<strong>session.use_only_cookies:</strong> " . ini_get('session.use_only_cookies') . "<br>";
echo "<strong>session.cookie_secure:</strong> " . ini_get('session.cookie_secure') . "<br>";
echo "<strong>session.cookie_httponly:</strong> " . ini_get('session.cookie_httponly') . "<br>";
echo "</div>";

// Test login functionality
if (isset($_POST['test_login'])) {
    echo "<div class='info'>";
    echo "<h2>🧪 Test Login Result</h2>";
    
    $username = $_POST['username'];
    $password = $_POST['password'];
    $salt = '%^&$#@*!';
    $hashed_password = md5($salt . $password);
    
    include_once '../databaseConn.php';
    $DatabaseCo = new DatabaseConn();
    
    $SQL_STATEMENT = "SELECT * FROM admin_users WHERE uname='" . mysqli_real_escape_string($DatabaseCo->dbLink, $username) . "' AND pswd='" . $hashed_password . "' AND role_id='1'";
    echo "<strong>SQL Query:</strong> " . $SQL_STATEMENT . "<br>";
    
    $result = $DatabaseCo->dbLink->query($SQL_STATEMENT);
    if ($result && mysqli_num_rows($result) > 0) {
        $user = mysqli_fetch_object($result);
        echo "<div class='success'>✅ User found in database</div>";
        echo "<strong>Username:</strong> " . $user->uname . "<br>";
        echo "<strong>Email:</strong> " . $user->email . "<br>";
        echo "<strong>User ID:</strong> " . $user->id . "<br>";
        
        // Set session variables
        $_SESSION['admin_user_name'] = $user->uname;
        $_SESSION['admin_email'] = $user->email;
        $_SESSION['admin_user_id'] = $user->id;
        
        echo "<div class='success'>✅ Session variables set successfully</div>";
    } else {
        echo "<div class='error'>❌ User not found or incorrect credentials</div>";
        echo "<strong>Error:</strong> " . mysqli_error($DatabaseCo->dbLink) . "<br>";
    }
    echo "</div>";
}

// Test form
echo "<div class='info'>";
echo "<h2>🔑 Test Login</h2>";
echo "<form method='post'>";
echo "<div style='margin: 10px 0;'>";
echo "<label>Username:</label><br>";
echo "<input type='text' name='username' style='width: 200px; padding: 5px;' required>";
echo "</div>";
echo "<div style='margin: 10px 0;'>";
echo "<label>Password:</label><br>";
echo "<input type='password' name='password' style='width: 200px; padding: 5px;' required>";
echo "</div>";
echo "<button type='submit' name='test_login' class='btn btn-success'>Test Login</button>";
echo "</form>";
echo "</div>";

// Action buttons
echo "<div class='info'>";
echo "<h2>🎯 Quick Actions</h2>";
echo "<a href='index.php' class='btn'>Go to Login Page</a>";
echo "<a href='dashboard.php' class='btn'>Go to Dashboard</a>";
echo "<a href='debug_session.php' class='btn'>Refresh Debug</a>";
echo "<a href='index.php?option=logout' class='btn btn-danger'>Logout</a>";
echo "</div>";

// Recommendations
echo "<div class='info'>";
echo "<h2>💡 Troubleshooting Steps</h2>";
echo "<ol>";
echo "<li><strong>Clear Browser Cache:</strong> Clear cookies and cache for this domain</li>";
echo "<li><strong>Check Session Files:</strong> Ensure session directory is writable</li>";
echo "<li><strong>Verify Database:</strong> Check if admin_users table exists and has data</li>";
echo "<li><strong>Check .htaccess:</strong> Ensure URL rewriting is working correctly</li>";
echo "<li><strong>PHP Settings:</strong> Verify session configuration is correct</li>";
echo "</ol>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
