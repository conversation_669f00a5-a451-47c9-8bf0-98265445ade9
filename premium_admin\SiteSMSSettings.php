<?php
include_once '../databaseConn.php';
include_once '../class/Config.class.php';
$configObj = new Config();
include_once '../lib/requestHandler.php';
$DatabaseCo = new DatabaseConn();

// Create SMS settings table if it doesn't exist
$create_table_sql = "CREATE TABLE IF NOT EXISTS `sms_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `provider` varchar(50) NOT NULL DEFAULT 'fast2sms',
  `fast2sms_api_key` varchar(255) DEFAULT NULL,
  `fast2sms_sender_id` varchar(10) DEFAULT NULL,
  `fast2sms_route` varchar(20) DEFAULT 'otp',
  `msg91_api_key` varchar(255) DEFAULT NULL,
  `msg91_sender_id` varchar(10) DEFAULT NULL,
  `textlocal_api_key` varchar(255) DEFAULT NULL,
  `textlocal_sender_id` varchar(10) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `test_mobile` varchar(15) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

$DatabaseCo->dbLink->query($create_table_sql);

// Insert default record if none exists
$check_record = $DatabaseCo->dbLink->query("SELECT COUNT(*) as count FROM sms_settings");
$count_result = mysqli_fetch_object($check_record);
if ($count_result->count == 0) {
    $DatabaseCo->dbLink->query("INSERT INTO sms_settings (provider, fast2sms_api_key, fast2sms_sender_id, fast2sms_route) VALUES ('fast2sms', 'r2oeVHLQzpadwlNAGiEXtBfguK1IWS7YxZ58ROqn3DhMFvy9UbjN4C6iqf59nFPW8yD0wuZslmAJRtOr', 'FSTSMS', 'otp')");
}

$msg = "";
$test_result = "";

// Handle form submission
if (isset($_REQUEST['update_sms_settings'])) {
    $provider = mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['provider']);
    $fast2sms_api_key = mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['fast2sms_api_key']);
    $fast2sms_sender_id = mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['fast2sms_sender_id']);
    $fast2sms_route = mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['fast2sms_route']);
    $msg91_api_key = mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['msg91_api_key']);
    $msg91_sender_id = mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['msg91_sender_id']);
    $textlocal_api_key = mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['textlocal_api_key']);
    $textlocal_sender_id = mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['textlocal_sender_id']);
    $test_mobile = mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['test_mobile']);

    $update_sql = "UPDATE sms_settings SET 
        provider='$provider',
        fast2sms_api_key='$fast2sms_api_key',
        fast2sms_sender_id='$fast2sms_sender_id',
        fast2sms_route='$fast2sms_route',
        msg91_api_key='$msg91_api_key',
        msg91_sender_id='$msg91_sender_id',
        textlocal_api_key='$textlocal_api_key',
        textlocal_sender_id='$textlocal_sender_id',
        test_mobile='$test_mobile'
        WHERE id='1'";

    if ($DatabaseCo->dbLink->query($update_sql)) {
        $msg = "SMS settings updated successfully.";
        
        // Update mobile-apis.php file
        $mobile_apis_content = "<?php\n";
        $mobile_apis_content .= "// Auto-generated SMS API configuration\n";
        $mobile_apis_content .= "// Last updated: " . date('Y-m-d H:i:s') . "\n\n";
        
        if ($provider == 'fast2sms') {
            if ($fast2sms_route == 'otp') {
                $mobile_apis_content .= "\$url = \"https://www.fast2sms.com/dev/bulkV2?authorization=$fast2sms_api_key&route=otp&variables_values=\$order_id&flash=0&numbers=\$mno\";\n";
            } else {
                $mobile_apis_content .= "\$url = \"https://www.fast2sms.com/dev/bulkV2?authorization=$fast2sms_api_key&route=dlt&sender_id=$fast2sms_sender_id&message=Your%20OTP%20is%20\$order_id.%20Do%20not%20share%20with%20anyone.&variables_values=\$order_id&flash=0&numbers=\$mno\";\n";
            }
        } elseif ($provider == 'msg91') {
            $mobile_apis_content .= "\$url = \"https://control.msg91.com/api/sendhttp.php?authkey=$msg91_api_key&mobiles=\$mno&message=Your%20OTP%20is%20\$order_id.%20Do%20not%20share%20with%20anyone.&sender=$msg91_sender_id&route=4&country=91\";\n";
        } elseif ($provider == 'textlocal') {
            $mobile_apis_content .= "\$url = \"https://api.textlocal.in/send/?apikey=$textlocal_api_key&numbers=\$mno&message=Your%20OTP%20is%20\$order_id.%20Do%20not%20share%20with%20anyone.&sender=$textlocal_sender_id\";\n";
        }
        
        $mobile_apis_content .= "?>";
        
        file_put_contents('../mobile-apis.php', $mobile_apis_content);
    } else {
        $msg = "Error updating SMS settings: " . mysqli_error($DatabaseCo->dbLink);
    }
}

// Handle test SMS
if (isset($_REQUEST['test_sms'])) {
    $test_mobile = mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['test_mobile_number']);
    
    // Include the SMS handler
    include_once '../sms_handler.php';
    $sms = new SMSHandler(true);
    $test_otp = rand(1000, 9999);
    $result = $sms->sendOTP($test_mobile, $test_otp);
    
    if ($result['success']) {
        $test_result = "<div class='alert alert-success'>✅ Test SMS sent successfully! OTP: $test_otp</div>";
    } else {
        $test_result = "<div class='alert alert-danger'>❌ Test SMS failed: " . htmlspecialchars($result['message']) . "</div>";
    }
}

// Get current settings
$sql = $DatabaseCo->dbLink->query("SELECT * FROM sms_settings WHERE id='1'");
$data = mysqli_fetch_object($sql);
if (!$data) {
    // Create default record
    $DatabaseCo->dbLink->query("INSERT INTO sms_settings (id) VALUES (1)");
    $sql = $DatabaseCo->dbLink->query("SELECT * FROM sms_settings WHERE id='1'");
    $data = mysqli_fetch_object($sql);
}
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Admin | SMS Settings</title>
    <meta content='width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no' name='viewport'>
    
    <!-- Bootstrap & custom css -->
    <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="css/custom.css" rel="stylesheet" type="text/css" />

    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/48403ccd1a.js" crossorigin="anonymous"></script>
    
    <!-- Ionicons -->
    <link href="http://code.ionicframework.com/ionicons/2.0.0/css/ionicons.min.css" rel="stylesheet" type="text/css" />
    
    <!-- Theme css -->
    <link href="dist/css/AdminLTE.min.css" rel="stylesheet" type="text/css" />
    <link href="dist/css/skins/_all-skins.min.css" rel="stylesheet" type="text/css" />
    
    <!-- Checkbox css -->
    <link href="plugins/iCheck/square/blue.css" rel="stylesheet" type="text/css" />
    
    <!-- Post Validation CSS -->
    <link href="css/postvalidationcss.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="../css/validate.css">
</head>

<body class="skin-blue">
    <!-- Icon Loader -->
    <div class="preloader-wrapper text-center">
        <div class="spinner"></div>
    </div>
    <!-- /. Icon Loader-->
    <div class="wrapper" style="display:none" id="body">
        <!-- Header & Menu -->
        <?php include "page-part/header.php"; ?> 
        <?php include "page-part/left_panel.php"; ?>
        <!-- /. Header & Menu -->
        <div class="content-wrapper">
            <section class="content-header">
                <h1 class="lightGrey">SMS Gateway Settings</h1>
                <ol class="breadcrumb">
                    <li><a href="dashboard"><i class="fa fa-home"></i> Home</a></li>
                    <li class="active">SMS Settings</li>
                </ol>
            </section>
            <section class="content">
                <div class="row">
                    <div class="box-body gtSiteChangeId">
                        <div class="box box-success">
                            <div class="box-body">
                                <?php if($msg != ""): ?>
                                    <div class="alert alert-success alert-dismissible">
                                        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                                        <h4><i class="icon fa fa-check"></i> Success!</h4>
                                        <?php echo $msg; ?>
                                    </div>
                                <?php endif; ?>
                                
                                <?php echo $test_result; ?>
                                
                                <form action="" method="post" enctype="multipart/form-data">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <h3><i class="fa fa-sms"></i> SMS Provider Configuration</h3>
                                            <hr>
                                            
                                            <div class="form-group">
                                                <label>Select SMS Provider</label>
                                                <select name="provider" class="form-control" id="sms_provider">
                                                    <option value="fast2sms" <?php echo ($data->provider == 'fast2sms') ? 'selected' : ''; ?>>Fast2SMS</option>
                                                    <option value="msg91" <?php echo ($data->provider == 'msg91') ? 'selected' : ''; ?>>MSG91</option>
                                                    <option value="textlocal" <?php echo ($data->provider == 'textlocal') ? 'selected' : ''; ?>>Textlocal</option>
                                                </select>
                                            </div>
                                            
                                            <!-- Fast2SMS Settings -->
                                            <div id="fast2sms_settings" class="provider-settings">
                                                <h4><i class="fa fa-bolt"></i> Fast2SMS Configuration</h4>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label>API Key</label>
                                                            <input type="text" name="fast2sms_api_key" class="form-control" value="<?php echo htmlspecialchars($data->fast2sms_api_key ?? ''); ?>" placeholder="Enter Fast2SMS API Key">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="form-group">
                                                            <label>Sender ID</label>
                                                            <input type="text" name="fast2sms_sender_id" class="form-control" value="<?php echo htmlspecialchars($data->fast2sms_sender_id ?? ''); ?>" placeholder="FSTSMS">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="form-group">
                                                            <label>Route</label>
                                                            <select name="fast2sms_route" class="form-control">
                                                                <option value="otp" <?php echo ($data->fast2sms_route == 'otp') ? 'selected' : ''; ?>>OTP Route</option>
                                                                <option value="dlt" <?php echo ($data->fast2sms_route == 'dlt') ? 'selected' : ''; ?>>DLT Route</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- MSG91 Settings -->
                                            <div id="msg91_settings" class="provider-settings" style="display:none;">
                                                <h4><i class="fa fa-envelope"></i> MSG91 Configuration</h4>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label>API Key</label>
                                                            <input type="text" name="msg91_api_key" class="form-control" value="<?php echo htmlspecialchars($data->msg91_api_key ?? ''); ?>" placeholder="Enter MSG91 API Key">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label>Sender ID</label>
                                                            <input type="text" name="msg91_sender_id" class="form-control" value="<?php echo htmlspecialchars($data->msg91_sender_id ?? ''); ?>" placeholder="Enter Sender ID">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- Textlocal Settings -->
                                            <div id="textlocal_settings" class="provider-settings" style="display:none;">
                                                <h4><i class="fa fa-mobile"></i> Textlocal Configuration</h4>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label>API Key</label>
                                                            <input type="text" name="textlocal_api_key" class="form-control" value="<?php echo htmlspecialchars($data->textlocal_api_key ?? ''); ?>" placeholder="Enter Textlocal API Key">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label>Sender ID</label>
                                                            <input type="text" name="textlocal_sender_id" class="form-control" value="<?php echo htmlspecialchars($data->textlocal_sender_id ?? ''); ?>" placeholder="Enter Sender ID">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label>Test Mobile Number</label>
                                                <input type="text" name="test_mobile" class="form-control" value="<?php echo htmlspecialchars($data->test_mobile ?? ''); ?>" placeholder="Enter mobile number for testing">
                                            </div>
                                            
                                            <div class="form-group">
                                                <button type="submit" name="update_sms_settings" class="btn btn-success">
                                                    <i class="fa fa-save"></i> Update SMS Settings
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                                
                                <!-- Test SMS Section -->
                                <div class="row">
                                    <div class="col-md-12">
                                        <h3><i class="fa fa-test-tube"></i> Test SMS</h3>
                                        <hr>
                                        <form action="" method="post">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Mobile Number</label>
                                                        <input type="text" name="test_mobile_number" class="form-control" placeholder="Enter mobile number to test" required>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>&nbsp;</label><br>
                                                        <button type="submit" name="test_sms" class="btn btn-primary">
                                                            <i class="fa fa-paper-plane"></i> Send Test SMS
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
        <?php include "page-part/footer.php"; ?>
    </div>

    <!-- jQuery -->
    <script src="plugins/jQuery/jQuery-2.1.3.min.js"></script>
    <!-- Bootstrap -->
    <script src="bootstrap/js/bootstrap.min.js" type="text/javascript"></script>
    <!-- AdminLTE App -->
    <script src="dist/js/app.min.js" type="text/javascript"></script>
    
    <script>
        $(document).ready(function() {
            $('#body').show();
            $('.preloader-wrapper').hide();
            
            // Show/hide provider settings based on selection
            $('#sms_provider').change(function() {
                $('.provider-settings').hide();
                $('#' + $(this).val() + '_settings').show();
            });
            
            // Initialize on page load
            $('#sms_provider').trigger('change');
        });
    </script>
</body>
</html>
