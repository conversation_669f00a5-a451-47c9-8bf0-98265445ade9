# 🔧 .htaccess Troubleshooting Guide

## 🚨 **Common .htaccess Issues Found**

### **1. HTTPS Redirect Conflicts**
**Problem**: Forced HTTPS redirects causing issues in local development
**Solution**: Conditional HTTPS redirects that exclude localhost

### **2. Redirect Loops**
**Problem**: WWW to non-WWW redirects causing infinite loops
**Solution**: Proper condition checking before redirects

### **3. Missing Security Headers**
**Problem**: No protection against clickjacking, XSS, etc.
**Solution**: Added comprehensive security headers

### **4. Performance Issues**
**Problem**: No compression or caching
**Solution**: Added Gzip compression and browser caching

## 🛠️ **Quick Fix Steps**

### **Step 1: Use the Fix Tool**
1. Open: `your-domain.com/fix_htaccess.php`
2. Click "Create Backup" first
3. Click "Apply New .htaccess"
4. Test your site functionality

### **Step 2: Manual Fix (if needed)**
If the automated tool doesn't work:

1. **Backup current files:**
   ```bash
   cp .htaccess .htaccess.backup
   cp premium_admin/.htaccess premium_admin/.htaccess.backup
   ```

2. **Replace with optimized versions:**
   ```bash
   cp .htaccess.new .htaccess
   cp premium_admin/.htaccess.new premium_admin/.htaccess
   ```

## 📋 **What's Been Fixed**

### **Root .htaccess Improvements:**
- ✅ **Security Headers**: Added X-Frame-Options, X-XSS-Protection, etc.
- ✅ **File Protection**: Block access to sensitive files (.env, config, backups)
- ✅ **Conditional Redirects**: HTTPS/WWW redirects only for production
- ✅ **Performance**: Gzip compression and browser caching
- ✅ **Clean URLs**: Better .php extension removal
- ✅ **PHP Settings**: Optimized memory and upload limits

### **Admin .htaccess Improvements:**
- ✅ **Enhanced Security**: Stricter access controls for admin area
- ✅ **Clean Admin URLs**: Remove .php extension from admin pages
- ✅ **Performance**: Optimized for admin operations
- ✅ **Error Handling**: Better error reporting for debugging

## 🔍 **Testing Your Fix**

### **1. Basic Functionality Test**
- ✅ Homepage loads correctly
- ✅ Login/registration works
- ✅ Admin panel accessible
- ✅ Clean URLs work (without .php extension)

### **2. SMS/OTP Test**
- ✅ OTP sending works
- ✅ SMS settings page accessible
- ✅ No 500 errors on form submissions

### **3. Security Test**
- ✅ Sensitive files blocked (try accessing .env, config.php)
- ✅ Directory listing disabled
- ✅ Security headers present (check browser dev tools)

### **4. Performance Test**
- ✅ Pages load faster
- ✅ CSS/JS files cached
- ✅ Images compressed

## 🚨 **Emergency Procedures**

### **If Site Breaks (500 Error):**
1. **Quick Fix**: Rename .htaccess to .htaccess.disabled
2. **Restore Backup**: Copy .htaccess.backup to .htaccess
3. **Check Logs**: Look at server error logs for specific issues

### **If Admin Panel Breaks:**
1. **Disable Admin .htaccess**: Rename premium_admin/.htaccess
2. **Access via direct URLs**: Use full .php extensions
3. **Restore Admin Backup**: Copy admin .htaccess backup

### **If Redirect Loops Occur:**
1. **Comment out redirect rules** in .htaccess:
   ```apache
   # RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
   # RewriteRule ^(.*)$ https://%1/$1 [R=301,L]
   ```

## 📊 **Before vs After Comparison**

### **Old .htaccess Issues:**
- ❌ Forced HTTPS in development
- ❌ No security headers
- ❌ No file protection
- ❌ No performance optimization
- ❌ Basic URL rewriting

### **New .htaccess Benefits:**
- ✅ Development-friendly redirects
- ✅ Comprehensive security
- ✅ Protected sensitive files
- ✅ Gzip compression + caching
- ✅ Advanced URL rewriting
- ✅ Better error handling

## 🔧 **Advanced Configuration**

### **For Production (Live Site):**
Uncomment these lines in .htaccess:
```apache
# Force HTTPS
RewriteCond %{HTTPS} off
RewriteCond %{HTTP_HOST} !^localhost [NC]
RewriteCond %{HTTP_HOST} !^127\.0\.0\.1 [NC]
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Redirect www to non-www
RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
RewriteRule ^(.*)$ https://%1/$1 [R=301,L]
```

### **For Enhanced Security:**
Add to .htaccess:
```apache
# Block bad bots
RewriteCond %{HTTP_USER_AGENT} (bot|crawler|spider) [NC]
RewriteRule ^(.*)$ - [F,L]

# Rate limiting (if mod_evasive available)
<IfModule mod_evasive24.c>
    DOSHashTableSize    2048
    DOSPageCount        20
    DOSPageInterval     1
    DOSSiteCount        50
    DOSSiteInterval     1
    DOSBlockingPeriod   600
</IfModule>
```

## 📞 **Support & Troubleshooting**

### **Common Error Messages:**
- **"Internal Server Error"**: Check .htaccess syntax
- **"Too many redirects"**: Comment out redirect rules
- **"File not found"**: Check RewriteRule patterns
- **"Access denied"**: Check file permissions

### **Debugging Steps:**
1. **Enable error reporting** in PHP
2. **Check server error logs**
3. **Test with minimal .htaccess**
4. **Use online .htaccess validators**

### **Tools for Testing:**
- **Fix Tool**: `your-domain.com/fix_htaccess.php`
- **OTP Diagnostic**: `your-domain.com/diagnose_otp_issue.php`
- **Browser Dev Tools**: Check network and console tabs
- **Online Validators**: htaccess.madewithlove.be

---

**🎉 Your .htaccess files are now optimized for security, performance, and functionality!**
