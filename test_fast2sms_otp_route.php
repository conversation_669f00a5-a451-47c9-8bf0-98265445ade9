<?php
/**
 * Fast2SMS OTP Route Test
 * Testing the exact OTP route format as per Fast2SMS documentation
 */

echo "<!DOCTYPE html>
<html>
<head>
    <title>Fast2SMS OTP Route Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: green; background: #e8f5e8; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid green; }
        .error { color: red; background: #ffe8e8; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid red; }
        .warning { color: orange; background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid orange; }
        .info { color: blue; background: #e8f0ff; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid blue; }
        .btn { display: inline-block; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; border: none; border-radius: 5px; margin: 5px; cursor: pointer; }
        .btn:hover { background: #005a87; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: black; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type='text'] { width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>";

echo "<div class='container'>";
echo "<h1>📱 Fast2SMS OTP Route Test</h1>";

// Handle form submission
if (isset($_POST['test_otp'])) {
    $mobile = $_POST['mobile'];
    $otp = $_POST['otp'];
    
    echo "<div class='info'>";
    echo "<h2>🧪 Testing OTP Send</h2>";
    echo "<strong>Mobile:</strong> $mobile<br>";
    echo "<strong>OTP:</strong> $otp<br>";
    echo "</div>";
    
    // Your exact API format
    $authorization = "r2oeVHLQzpadwlNAGiEXtBfguK1IWS7YxZ58ROqn3DhMFvy9UbjN4C6iqf59nFPW8yD0wuZslmAJRtOr";
    $route = "otp";
    $variables_values = $otp;
    $numbers = $mobile;
    $flash = "0";
    
    // Build URL exactly as per Fast2SMS docs
    $url = "https://www.fast2sms.com/dev/bulkV2?authorization=$authorization&route=$route&variables_values=$variables_values&flash=$flash&numbers=$numbers";
    
    echo "<div class='info'>";
    echo "<h3>📡 API Request Details</h3>";
    echo "<strong>URL:</strong><br>";
    echo "<pre>$url</pre>";
    echo "</div>";
    
    // Test with cURL
    echo "<div class='info'>";
    echo "<h3>📤 Sending Request...</h3>";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "<strong>HTTP Status Code:</strong> $http_code<br>";
    
    if ($error) {
        echo "<div class='error'>❌ cURL Error: $error</div>";
    } else {
        echo "<strong>Raw Response:</strong><br>";
        echo "<pre>$response</pre>";
        
        // Try to parse JSON response
        $response_data = json_decode($response, true);
        if ($response_data) {
            echo "<h3>📋 Parsed Response:</h3>";
            
            if (isset($response_data['return']) && $response_data['return'] == true) {
                echo "<div class='success'>";
                echo "<h3>✅ SUCCESS! OTP Sent Successfully</h3>";
                echo "<strong>Message ID:</strong> " . (isset($response_data['request_id']) ? $response_data['request_id'] : 'N/A') . "<br>";
                echo "<strong>Message:</strong> " . (isset($response_data['message']) ? $response_data['message'] : 'OTP sent successfully') . "<br>";
                echo "</div>";
            } else {
                echo "<div class='error'>";
                echo "<h3>❌ API Error</h3>";
                echo "<strong>Return Status:</strong> " . (isset($response_data['return']) ? ($response_data['return'] ? 'true' : 'false') : 'unknown') . "<br>";
                echo "<strong>Error Code:</strong> " . (isset($response_data['code']) ? $response_data['code'] : 'unknown') . "<br>";
                echo "<strong>Error Message:</strong> " . (isset($response_data['message']) ? $response_data['message'] : 'unknown') . "<br>";
                echo "</div>";
                
                // Specific error handling
                if (isset($response_data['code'])) {
                    $error_code = $response_data['code'];
                    
                    if ($error_code == '996') {
                        echo "<div class='warning'>";
                        echo "<h3>🔒 Website Verification Required</h3>";
                        echo "<p><strong>Issue:</strong> Your Fast2SMS account needs website verification to use OTP route.</p>";
                        echo "<p><strong>Solution:</strong></p>";
                        echo "<ol>";
                        echo "<li>Login to <a href='https://www.fast2sms.com/login' target='_blank'>Fast2SMS Dashboard</a></li>";
                        echo "<li>Navigate to <strong>'OTP Message'</strong> menu</li>";
                        echo "<li>Click on <strong>'Website Verification'</strong></li>";
                        echo "<li>Submit your website domain for verification</li>";
                        echo "<li>Upload required documents (if any)</li>";
                        echo "<li>Wait for approval (usually 24-48 hours)</li>";
                        echo "</ol>";
                        echo "<p><strong>After verification:</strong> Your OTP route will be activated and this API will work.</p>";
                        echo "</div>";
                    } elseif ($error_code == '101') {
                        echo "<div class='warning'>";
                        echo "<h3>🔑 Invalid API Key</h3>";
                        echo "<p>Your API key might be expired or invalid. Please check your Fast2SMS dashboard.</p>";
                        echo "</div>";
                    } elseif ($error_code == '102') {
                        echo "<div class='warning'>";
                        echo "<h3>💰 Insufficient Balance</h3>";
                        echo "<p>Your Fast2SMS account doesn't have enough balance to send SMS.</p>";
                        echo "</div>";
                    }
                }
            }
        } else {
            echo "<div class='warning'>⚠️ Could not parse JSON response. Raw response shown above.</div>";
        }
    }
    echo "</div>";
    
    // Test with file() function (as used in your original code)
    echo "<div class='info'>";
    echo "<h3>🔄 Testing with file() function</h3>";
    $file_response = @file($url);
    if ($file_response === false) {
        echo "<div class='error'>❌ file() function failed</div>";
    } else {
        echo "<div class='success'>✅ file() function worked</div>";
        echo "<strong>Response:</strong><br>";
        echo "<pre>" . implode('', $file_response) . "</pre>";
    }
    echo "</div>";
}

// Test form
echo "<div class='info'>";
echo "<h2>🧪 Test Fast2SMS OTP Route</h2>";
echo "<p>Test your Fast2SMS OTP API with a real mobile number:</p>";
echo "<form method='post'>";
echo "<div class='form-group'>";
echo "<label>Mobile Number (10 digits):</label>";
echo "<input type='text' name='mobile' placeholder='9876543210' required>";
echo "</div>";
echo "<div class='form-group'>";
echo "<label>OTP (4-6 digits):</label>";
echo "<input type='text' name='otp' value='" . rand(1000, 9999) . "' required>";
echo "</div>";
echo "<button type='submit' name='test_otp' class='btn btn-success'>Send Test OTP</button>";
echo "</form>";
echo "</div>";

// API Configuration
echo "<div class='info'>";
echo "<h2>⚙️ Current API Configuration</h2>";
echo "<strong>Authorization Key:</strong> r2oeVHLQzpadwlNAGiEXtBfguK1IWS7YxZ58ROqn3DhMFvy9UbjN4C6iqf59nFPW8yD0wuZslmAJRtOr<br>";
echo "<strong>Route:</strong> otp<br>";
echo "<strong>Flash:</strong> 0<br>";
echo "<strong>API Endpoint:</strong> https://www.fast2sms.com/dev/bulkV2<br>";
echo "</div>";

// Documentation reference
echo "<div class='info'>";
echo "<h2>📚 Fast2SMS Documentation</h2>";
echo "<p>Your API format is correct as per <a href='https://www.fast2sms.com/docs' target='_blank'>Fast2SMS Documentation</a></p>";
echo "<p><strong>Required Parameters for OTP Route:</strong></p>";
echo "<ul>";
echo "<li><strong>authorization:</strong> Your API key ✅</li>";
echo "<li><strong>route:</strong> otp ✅</li>";
echo "<li><strong>variables_values:</strong> OTP value ✅</li>";
echo "<li><strong>numbers:</strong> Mobile number ✅</li>";
echo "<li><strong>flash:</strong> 0 (for normal SMS) ✅</li>";
echo "</ul>";
echo "</div>";

// Next steps
echo "<div class='warning'>";
echo "<h2>🎯 Next Steps</h2>";
echo "<p><strong>If you get Error 996 (Website Verification Required):</strong></p>";
echo "<ol>";
echo "<li>Complete website verification in Fast2SMS dashboard</li>";
echo "<li>This is a one-time process</li>";
echo "<li>After verification, your OTP route will work perfectly</li>";
echo "</ol>";
echo "<p><strong>If you need immediate SMS functionality:</strong></p>";
echo "<ol>";
echo "<li>Use the admin panel SMS settings to switch to MSG91 or Textlocal</li>";
echo "<li>Or wait for Fast2SMS verification to complete</li>";
echo "</ol>";
echo "<a href='premium_admin/SiteSMSSettings.php' class='btn btn-success'>Configure SMS Settings</a>";
echo "<a href='https://www.fast2sms.com/login' target='_blank' class='btn btn-warning'>Fast2SMS Dashboard</a>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
