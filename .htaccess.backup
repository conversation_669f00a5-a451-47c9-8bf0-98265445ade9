# Enable mod_rewrite
<IfModule mod_rewrite.c>
    RewriteEngine On

    # If the request is for a file that doesn't exist
    RewriteCond %{REQUEST_FILENAME} !-f
    # If the request is for a directory that doesn't exist
    RewriteCond %{REQUEST_FILENAME} !-d
    # Rewrite the URL to append .php if a matching .php file exists
    RewriteRule ^(.*)$ $1.php [L,QSA]

    # Redirect www to non-www (optional)
    RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
    RewriteRule ^(.*)$ http://%1/$1 [R=301,L]

    # Redirect HTTP to HTTPS (optional)
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

    # Remove trailing slash (optional)
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [R=301,L]
</IfModule>

# php -- BEGIN cPanel-generated handler, do not edit
# This domain inherits the "PHP" package.
# php -- END cPanel-generated handler, do not edit
