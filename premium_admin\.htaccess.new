# ========================================
# ADMIN PANEL .HTACCESS CONFIGURATION
# ========================================

# ========================================
# SECURITY HEADERS
# ========================================
<IfModule mod_headers.c>
    # Prevent clickjacking
    Header always append X-Frame-Options DENY
    
    # Prevent MIME type sniffing
    Header set X-Content-Type-Options nosniff
    
    # Enable XSS protection
    Header set X-XSS-Protection "1; mode=block"
    
    # Strict referrer policy for admin
    Header set Referrer-Policy "same-origin"
    
    # Remove server signature
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# ========================================
# ACCESS CONTROL
# ========================================

# Block access to sensitive files
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

# Block access to backup files
<FilesMatch "\.(bak|backup|old|tmp|log)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# ========================================
# URL REWRITING
# ========================================
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Block direct access to sensitive files
    RewriteRule ^(config|database|includes|classes)/ - [F,L]
    
    # Clean URLs - Remove .php extension for admin pages
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME}.php -f
    RewriteRule ^(.*)$ $1.php [L,QSA]
    
    # Remove trailing slash
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [R=301,L]
    
    # Redirect to dashboard if accessing admin root
    RewriteRule ^$ dashboard [R=301,L]
</IfModule>

# ========================================
# PERFORMANCE OPTIMIZATION
# ========================================

# Enable Gzip Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Browser Caching for admin assets
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 week"
    ExpiresByType application/javascript "access plus 1 week"
    ExpiresByType image/png "access plus 1 week"
    ExpiresByType image/jpg "access plus 1 week"
    ExpiresByType image/jpeg "access plus 1 week"
    ExpiresByType image/gif "access plus 1 week"
</IfModule>

# ========================================
# PHP SETTINGS FOR ADMIN
# ========================================
<IfModule mod_php7.c>
    # Increase limits for admin operations
    php_value memory_limit 512M
    php_value max_execution_time 300
    php_value upload_max_filesize 50M
    php_value post_max_size 50M
    
    # Enhanced security for admin
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 0
    php_value session.use_strict_mode 1
    
    # Error reporting for admin (disable in production)
    php_flag display_errors on
    php_flag log_errors on
</IfModule>

# ========================================
# DIRECTORY PROTECTION
# ========================================
Options -Indexes
Options -ExecCGI

# Additional protection for admin files
<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

<Files "*.sql">
    Order allow,deny
    Deny from all
</Files>
