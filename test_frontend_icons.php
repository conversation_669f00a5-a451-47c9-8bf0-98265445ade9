<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Frontend Icon Test - Matrimony Site</title>
    
    <!-- CSS Files (Same as main site) -->
    <link href="css/bootstrap.css" rel="stylesheet">
    <link href="css/custom-responsive.css" rel="stylesheet">
    <link href="css/custom.css" rel="stylesheet">
    <link href="css/icon-fixes.css" rel="stylesheet">
    <link href="css/mobile-responsive-fixes.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/48403ccd1a.js" crossorigin="anonymous"></script>
    
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .icon-test-section { background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #007cba; }
        .icon-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .icon-item { background: white; padding: 15px; border-radius: 8px; text-align: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .icon-item i { font-size: 24px; margin-bottom: 10px; color: #007cba; }
        .icon-item.working i { color: #28a745; }
        .icon-item.broken i { color: #dc3545; }
        .icon-item.broken:after { content: " ❌ Not Working"; color: #dc3545; font-size: 12px; display: block; }
        .icon-item.working:after { content: " ✅ Working"; color: #28a745; font-size: 12px; display: block; }
        .success { color: green; background: #e8f5e8; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid green; }
        .error { color: red; background: #ffe8e8; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid red; }
        .warning { color: orange; background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid orange; }
        .info { color: blue; background: #e8f0ff; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid blue; }
        .btn { display: inline-block; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; border: none; border-radius: 5px; margin: 5px; cursor: pointer; }
        .btn:hover { background: #005a87; text-decoration: none; color: white; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; }
        .btn-phonepe { background: linear-gradient(135deg, #5f27cd 0%, #7c4dff 100%); }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Frontend Icon Display Test</h1>
        <p class="text-muted">Testing all icons used in the matrimony website frontend</p>
        
        <!-- Font Awesome Kit Status -->
        <div class="success">
            <h3>📡 Font Awesome Kit Status</h3>
            <p><strong>Kit URL:</strong> https://kit.fontawesome.com/48403ccd1a.js</p>
            <p><strong>Status:</strong> <span id="fa-status">Checking...</span></p>
            <p><strong>Icons Loaded:</strong> <span id="fa-count">Counting...</span></p>
        </div>

        <!-- PhonePe Payment Icons -->
        <div class="icon-test-section">
            <h3><i class="fa fa-credit-card"></i> PhonePe Payment Icons</h3>
            <div class="icon-grid">
                <div class="icon-item">
                    <i class="fa fa-crown"></i>
                    <div>Crown (Plan)</div>
                </div>
                <div class="icon-item">
                    <i class="fa fa-user"></i>
                    <div>User (Profile)</div>
                </div>
                <div class="icon-item">
                    <i class="fa fa-shield-alt"></i>
                    <div>Shield (Security)</div>
                </div>
                <div class="icon-item">
                    <i class="fa fa-mobile-alt"></i>
                    <div>Mobile (UPI)</div>
                </div>
                <div class="icon-item">
                    <i class="fa fa-clock"></i>
                    <div>Clock (Instant)</div>
                </div>
                <div class="icon-item">
                    <i class="fa fa-headset"></i>
                    <div>Headset (Support)</div>
                </div>
                <div class="icon-item">
                    <i class="fa fa-lock"></i>
                    <div>Lock (Secure)</div>
                </div>
                <div class="icon-item">
                    <i class="fa fa-credit-card"></i>
                    <div>Credit Card (Pay)</div>
                </div>
                <div class="icon-item">
                    <i class="fa fa-arrow-left"></i>
                    <div>Arrow Left (Back)</div>
                </div>
                <div class="icon-item">
                    <i class="fa fa-spinner fa-spin"></i>
                    <div>Spinner (Loading)</div>
                </div>
                <div class="icon-item">
                    <i class="fa fa-exclamation-triangle"></i>
                    <div>Warning (Error)</div>
                </div>
                <div class="icon-item">
                    <i class="fa fa-check-circle"></i>
                    <div>Check (Success)</div>
                </div>
            </div>
        </div>

        <!-- Common Website Icons -->
        <div class="icon-test-section">
            <h3><i class="fa fa-home"></i> Common Website Icons</h3>
            <div class="icon-grid">
                <div class="icon-item">
                    <i class="fa fa-home"></i>
                    <div>Home</div>
                </div>
                <div class="icon-item">
                    <i class="fa fa-search"></i>
                    <div>Search</div>
                </div>
                <div class="icon-item">
                    <i class="fa fa-envelope"></i>
                    <div>Email</div>
                </div>
                <div class="icon-item">
                    <i class="fa fa-phone"></i>
                    <div>Phone</div>
                </div>
                <div class="icon-item">
                    <i class="fa fa-heart"></i>
                    <div>Heart</div>
                </div>
                <div class="icon-item">
                    <i class="fa fa-camera"></i>
                    <div>Camera</div>
                </div>
                <div class="icon-item">
                    <i class="fa fa-upload"></i>
                    <div>Upload</div>
                </div>
                <div class="icon-item">
                    <i class="fa fa-download"></i>
                    <div>Download</div>
                </div>
                <div class="icon-item">
                    <i class="fa fa-cog"></i>
                    <div>Settings</div>
                </div>
                <div class="icon-item">
                    <i class="fa fa-edit"></i>
                    <div>Edit</div>
                </div>
                <div class="icon-item">
                    <i class="fa fa-trash"></i>
                    <div>Delete</div>
                </div>
                <div class="icon-item">
                    <i class="fa fa-plus"></i>
                    <div>Add</div>
                </div>
            </div>
        </div>

        <!-- Social Media Icons -->
        <div class="icon-test-section">
            <h3><i class="fab fa-facebook"></i> Social Media Icons</h3>
            <div class="icon-grid">
                <div class="icon-item">
                    <i class="fab fa-facebook-square"></i>
                    <div>Facebook</div>
                </div>
                <div class="icon-item">
                    <i class="fab fa-twitter-square"></i>
                    <div>Twitter</div>
                </div>
                <div class="icon-item">
                    <i class="fab fa-linkedin"></i>
                    <div>LinkedIn</div>
                </div>
                <div class="icon-item">
                    <i class="fab fa-pinterest-square"></i>
                    <div>Pinterest</div>
                </div>
                <div class="icon-item">
                    <i class="fab fa-instagram"></i>
                    <div>Instagram</div>
                </div>
                <div class="icon-item">
                    <i class="fab fa-youtube"></i>
                    <div>YouTube</div>
                </div>
                <div class="icon-item">
                    <i class="fab fa-whatsapp"></i>
                    <div>WhatsApp</div>
                </div>
                <div class="icon-item">
                    <i class="fab fa-google"></i>
                    <div>Google</div>
                </div>
            </div>
        </div>

        <!-- Payment Status Icons -->
        <div class="icon-test-section">
            <h3><i class="fa fa-rupee-sign"></i> Payment & Status Icons</h3>
            <div class="icon-grid">
                <div class="icon-item">
                    <i class="fa fa-rupee-sign"></i>
                    <div>Rupee</div>
                </div>
                <div class="icon-item">
                    <i class="fa fa-money-bill"></i>
                    <div>Money</div>
                </div>
                <div class="icon-item">
                    <i class="fa fa-wallet"></i>
                    <div>Wallet</div>
                </div>
                <div class="icon-item">
                    <i class="fa fa-receipt"></i>
                    <div>Receipt</div>
                </div>
                <div class="icon-item">
                    <i class="fa fa-check"></i>
                    <div>Success</div>
                </div>
                <div class="icon-item">
                    <i class="fa fa-times"></i>
                    <div>Failed</div>
                </div>
                <div class="icon-item">
                    <i class="fa fa-info-circle"></i>
                    <div>Info</div>
                </div>
                <div class="icon-item">
                    <i class="fa fa-refresh"></i>
                    <div>Refresh</div>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div id="test-results" class="info">
            <h3><i class="fa fa-clipboard-check"></i> Test Results</h3>
            <p><strong>Total Icons Tested:</strong> <span id="total-icons">0</span></p>
            <p><strong>Working Icons:</strong> <span id="working-icons">0</span></p>
            <p><strong>Broken Icons:</strong> <span id="broken-icons">0</span></p>
            <p><strong>Success Rate:</strong> <span id="success-rate">0%</span></p>
        </div>

        <!-- Quick Actions -->
        <div class="icon-test-section">
            <h3><i class="fa fa-tools"></i> Quick Actions</h3>
            <div class="text-center">
                <a href="phonepe_payment_form.php?pid=1" class="btn btn-phonepe">
                    <i class="fa fa-credit-card"></i> Test PhonePe Payment Form
                </a>
                <a href="paymentOptions.php?pid=1" class="btn btn-success">
                    <i class="fa fa-list"></i> Test Payment Options
                </a>
                <a href="index.php" class="btn btn-warning">
                    <i class="fa fa-home"></i> Back to Homepage
                </a>
                <button onclick="reloadIcons()" class="btn btn-danger">
                    <i class="fa fa-refresh"></i> Reload Icon Test
                </button>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center" style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">
            <p class="text-muted">
                <strong>Frontend Icon Test</strong> - 
                Last Updated: <?php echo date('Y-m-d H:i:s'); ?>
            </p>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/jquery.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/icon-loader.js"></script>
    <script>
        $(document).ready(function() {
            console.log('🔍 Starting Frontend Icon Test...');
            
            // Check Font Awesome Kit Status
            setTimeout(function() {
                checkFontAwesomeStatus();
                testAllIcons();
            }, 1000);
        });
        
        function checkFontAwesomeStatus() {
            // Check if Font Awesome is loaded
            var faLoaded = false;
            var faCount = 0;
            
            try {
                // Check if Font Awesome CSS is loaded
                var testElement = $('<i class="fa fa-home"></i>').appendTo('body');
                var computedStyle = window.getComputedStyle(testElement[0], ':before');
                
                if (computedStyle.content && computedStyle.content !== 'none' && computedStyle.content !== '""') {
                    faLoaded = true;
                    $('#fa-status').html('<span style="color: green;">✅ Loaded Successfully</span>');
                } else {
                    $('#fa-status').html('<span style="color: red;">❌ Failed to Load</span>');
                }
                
                testElement.remove();
                
                // Count available icons
                faCount = $('i[class*="fa"]').length;
                $('#fa-count').text(faCount + ' icons found');
                
            } catch (e) {
                $('#fa-status').html('<span style="color: red;">❌ Error: ' + e.message + '</span>');
            }
        }
        
        function testAllIcons() {
            var totalIcons = 0;
            var workingIcons = 0;
            var brokenIcons = 0;
            
            $('.icon-item').each(function() {
                totalIcons++;
                var $icon = $(this).find('i');
                var $item = $(this);
                
                // Test if icon is displaying
                setTimeout(function() {
                    var computedStyle = window.getComputedStyle($icon[0], ':before');
                    
                    if (computedStyle.content && computedStyle.content !== 'none' && computedStyle.content !== '""') {
                        $item.addClass('working');
                        workingIcons++;
                    } else {
                        $item.addClass('broken');
                        brokenIcons++;
                    }
                    
                    // Update results
                    updateTestResults(totalIcons, workingIcons, brokenIcons);
                }, 100);
            });
        }
        
        function updateTestResults(total, working, broken) {
            $('#total-icons').text(total);
            $('#working-icons').text(working);
            $('#broken-icons').text(broken);
            
            var successRate = total > 0 ? Math.round((working / total) * 100) : 0;
            $('#success-rate').text(successRate + '%');
            
            // Update result box color
            var $results = $('#test-results');
            $results.removeClass('info success warning error');
            
            if (successRate >= 90) {
                $results.addClass('success');
            } else if (successRate >= 70) {
                $results.addClass('warning');
            } else {
                $results.addClass('error');
            }
            
            console.log('📊 Icon Test Results:', {
                total: total,
                working: working,
                broken: broken,
                successRate: successRate + '%'
            });
        }
        
        function reloadIcons() {
            location.reload();
        }
        
        // Log icon status
        console.log('🎨 Frontend Icon Test Page Loaded');
        console.log('📡 Font Awesome Kit: https://kit.fontawesome.com/48403ccd1a.js');
        console.log('🔧 Icon Fixes: css/icon-fixes.css');
    </script>
</body>
</html>
