<?php
/**
 * Admin Session Fix Script
 * This script helps fix admin session issues
 */

include_once '../databaseConn.php';
$DatabaseCo = new DatabaseConn();

$message = "";
$session_fixed = false;

// Handle manual session creation
if (isset($_POST['create_session'])) {
    $username = mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['username']);
    $password = $_POST['password'];
    $salt = '%^&$#@*!';
    $hashed_password = md5($salt . $password);
    
    $SQL_STATEMENT = "SELECT * FROM admin_users WHERE uname='$username' AND pswd='$hashed_password' AND role_id='1'";
    $result = $DatabaseCo->dbLink->query($SQL_STATEMENT);
    
    if ($result && mysqli_num_rows($result) > 0) {
        $user = mysqli_fetch_object($result);
        
        // Set session variables
        $_SESSION['admin_user_name'] = $user->uname;
        $_SESSION['admin_email'] = $user->email;
        $_SESSION['admin_user_id'] = $user->id;
        
        $message = "<div class='success'>✅ Session created successfully! You can now access admin pages.</div>";
        $session_fixed = true;
    } else {
        $message = "<div class='error'>❌ Invalid username or password.</div>";
    }
}

// Handle session clearing
if (isset($_POST['clear_session'])) {
    session_destroy();
    session_start();
    $message = "<div class='warning'>⚠️ Session cleared. Please login again.</div>";
}

echo "<!DOCTYPE html>
<html>
<head>
    <title>Admin Session Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: green; background: #e8f5e8; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid green; }
        .error { color: red; background: #ffe8e8; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid red; }
        .warning { color: orange; background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid orange; }
        .info { color: blue; background: #e8f0ff; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid blue; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type='text'], input[type='password'] { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; box-sizing: border-box; }
        .btn { display: inline-block; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; border: none; border-radius: 5px; margin: 5px; cursor: pointer; }
        .btn:hover { background: #005a87; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; }
    </style>
</head>
<body>";

echo "<div class='container'>";
echo "<h1>🔧 Admin Session Fix Tool</h1>";

echo $message;

// Show current session status
echo "<div class='info'>";
echo "<h2>📊 Current Status</h2>";
if (isset($_SESSION['admin_user_name'])) {
    echo "<strong>✅ Logged in as:</strong> " . $_SESSION['admin_user_name'] . "<br>";
    echo "<strong>Email:</strong> " . $_SESSION['admin_email'] . "<br>";
    echo "<strong>User ID:</strong> " . $_SESSION['admin_user_id'] . "<br>";
    
    if ($session_fixed) {
        echo "<div class='success'>";
        echo "<h3>🎉 Session Fixed!</h3>";
        echo "<p>You can now access admin pages:</p>";
        echo "<a href='dashboard.php' class='btn btn-success'>Go to Dashboard</a>";
        echo "<a href='SiteSMSSettings.php' class='btn btn-success'>SMS Settings</a>";
        echo "<a href='SiteThemeSettings.php' class='btn btn-success'>Theme Settings</a>";
        echo "</div>";
    }
} else {
    echo "<strong>❌ Not logged in</strong>";
}
echo "</div>";

// Login form
if (!isset($_SESSION['admin_user_name'])) {
    echo "<div class='info'>";
    echo "<h2>🔑 Create Admin Session</h2>";
    echo "<p>Enter your admin credentials to create a session:</p>";
    echo "<form method='post'>";
    echo "<div class='form-group'>";
    echo "<label>Username:</label>";
    echo "<input type='text' name='username' required>";
    echo "</div>";
    echo "<div class='form-group'>";
    echo "<label>Password:</label>";
    echo "<input type='password' name='password' required>";
    echo "</div>";
    echo "<button type='submit' name='create_session' class='btn btn-success'>Create Session</button>";
    echo "</form>";
    echo "</div>";
}

// Session management
echo "<div class='info'>";
echo "<h2>🛠️ Session Management</h2>";
echo "<form method='post' style='display: inline;'>";
echo "<button type='submit' name='clear_session' class='btn btn-warning'>Clear Session</button>";
echo "</form>";
echo "<a href='debug_session.php' class='btn'>Debug Session</a>";
echo "<a href='index.php' class='btn'>Login Page</a>";
echo "</div>";

// Check admin users table
echo "<div class='info'>";
echo "<h2>👥 Admin Users Check</h2>";
$admin_check = $DatabaseCo->dbLink->query("SELECT COUNT(*) as count FROM admin_users WHERE role_id='1'");
if ($admin_check) {
    $admin_count = mysqli_fetch_object($admin_check);
    echo "<strong>Admin users found:</strong> " . $admin_count->count . "<br>";
    
    if ($admin_count->count == 0) {
        echo "<div class='error'>❌ No admin users found! You need to create an admin user first.</div>";
        echo "<p>You can create an admin user by running this SQL query:</p>";
        echo "<pre>INSERT INTO admin_users (uname, pswd, email, role_id) VALUES ('admin', '" . md5('%^&$#@*!' . 'admin123') . "', '<EMAIL>', '1');</pre>";
    } else {
        // Show admin users
        $users_query = $DatabaseCo->dbLink->query("SELECT uname, email FROM admin_users WHERE role_id='1'");
        echo "<strong>Available admin users:</strong><br>";
        while ($user = mysqli_fetch_object($users_query)) {
            echo "• " . $user->uname . " (" . $user->email . ")<br>";
        }
    }
} else {
    echo "<div class='error'>❌ Cannot check admin_users table: " . mysqli_error($DatabaseCo->dbLink) . "</div>";
}
echo "</div>";

// Troubleshooting tips
echo "<div class='info'>";
echo "<h2>💡 Troubleshooting Tips</h2>";
echo "<ol>";
echo "<li><strong>Clear browser cache and cookies</strong> for this domain</li>";
echo "<li><strong>Check if sessions are working</strong> by refreshing this page</li>";
echo "<li><strong>Verify admin credentials</strong> in the database</li>";
echo "<li><strong>Check .htaccess files</strong> for redirect issues</li>";
echo "<li><strong>Ensure session directory is writable</strong></li>";
echo "</ol>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
