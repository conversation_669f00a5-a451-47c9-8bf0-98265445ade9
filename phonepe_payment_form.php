<?php
/**
 * PhonePe Payment Form
 * Initiates payment process with PhonePe
 */

include_once 'databaseConn.php';
include_once 'lib/requestHandler.php';
include_once 'phonepe_integration.php';
include_once './class/Config.class.php';

$configObj = new Config();
$DatabaseCo = new DatabaseConn();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    if (isset($_POST['planid'])) {
        setcookie("planid", $_POST['planid'], time() + (60 * 1), "/");
    }
    echo "<script>window.location='login.php';</script>";
    exit;
}

$mid = $_SESSION['user_id'];
$planid = isset($_GET['pid']) ? $_GET['pid'] : (isset($_COOKIE['planid']) ? $_COOKIE['planid'] : '');

if (empty($planid)) {
    echo "<script>alert('Invalid plan selected.');</script>";
    echo "<script>window.location='membershipplans.php';</script>";
    exit;
}

// Get plan details
$plan_query = $DatabaseCo->dbLink->query("SELECT * FROM membership_plan WHERE plan_id='$planid'");
$plan_data = mysqli_fetch_object($plan_query);

if (!$plan_data) {
    echo "<script>alert('Plan not found.');</script>";
    echo "<script>window.location='membershipplans.php';</script>";
    exit;
}

// Get user details
$user_query = $DatabaseCo->dbLink->query("SELECT * FROM register WHERE matri_id='$mid'");
$user_data = mysqli_fetch_object($user_query);

// Get PhonePe configuration
$phonepe_config = PhonePeConfig::getConfig($DatabaseCo->dbLink);

if (!$phonepe_config || $phonepe_config->status != 'APPROVED') {
    echo "<script>alert('PhonePe payment is currently not available.');</script>";
    echo "<script>window.location='paymentOptions.php?pid=$planid';</script>";
    exit;
}

// Process payment if form is submitted
if (isset($_POST['initiate_payment'])) {
    try {
        // Initialize PhonePe
        $phonepe = new PhonePePayment(
            $phonepe_config->phonepe_merchant_id,
            $phonepe_config->phonepe_salt_key,
            $phonepe_config->phonepe_salt_index,
            $phonepe_config->phonepe_is_production == 'YES'
        );
        
        // Generate unique transaction ID
        $transaction_id = PhonePeUtils::generateTransactionId('MAT');
        
        // Validate mobile number
        $mobile = PhonePeUtils::validateMobile($user_data->mobile);
        if (!$mobile) {
            throw new Exception('Invalid mobile number. Please update your profile with a valid mobile number.');
        }
        
        // Prepare user data for payment
        $payment_user_data = [
            'user_id' => $user_data->matri_id,
            'mobile' => $mobile,
            'email' => $user_data->email,
            'name' => $user_data->first_name . ' ' . $user_data->last_name
        ];
        
        // Store transaction details in session
        $_SESSION['phonepe_transaction_id'] = $transaction_id;
        $_SESSION['phonepe_plan_id'] = $planid;
        $_SESSION['phonepe_amount'] = $plan_data->plan_amount;
        
        // Create payment request
        $payment_response = $phonepe->createPayment($transaction_id, $plan_data->plan_amount, $payment_user_data);
        
        // Log transaction
        PhonePeUtils::logTransaction($transaction_id, [
            'action' => 'payment_initiated',
            'user_id' => $mid,
            'plan_id' => $planid,
            'amount' => $plan_data->plan_amount,
            'response' => $payment_response
        ]);
        
        if ($payment_response['success'] && isset($payment_response['data']['data']['instrumentResponse']['redirectInfo']['url'])) {
            // Redirect to PhonePe payment page
            $payment_url = $payment_response['data']['data']['instrumentResponse']['redirectInfo']['url'];
            header("Location: $payment_url");
            exit;
        } else {
            throw new Exception('Failed to initiate payment. Please try again.');
        }
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
        PhonePeUtils::logTransaction($transaction_id ?? 'UNKNOWN', [
            'action' => 'payment_error',
            'error' => $error_message
        ], 'ERROR');
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>PhonePe Payment - <?php echo $configObj->getConfigTitle(); ?></title>
    
    <!-- CSS Files -->
    <link href="css/bootstrap.css" rel="stylesheet">
    <link href="css/custom.css" rel="stylesheet">
    <link href="css/icon-fixes.css" rel="stylesheet">
    <link href="css/mobile-responsive-fixes.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/48403ccd1a.js" crossorigin="anonymous"></script>
    
    <style>
        .phonepe-container {
            max-width: 600px;
            margin: 50px auto;
            padding: 30px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .phonepe-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        .phonepe-logo {
            width: 150px;
            height: auto;
            margin-bottom: 15px;
        }
        .plan-details {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .plan-details h4 {
            color: #5f27cd;
            margin-bottom: 15px;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .detail-row:last-child {
            border-bottom: none;
            font-weight: bold;
            font-size: 18px;
            color: #5f27cd;
        }
        .phonepe-btn {
            background: linear-gradient(135deg, #5f27cd 0%, #7c4dff 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .phonepe-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(95, 39, 205, 0.3);
        }
        .phonepe-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        .security-info {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        .error-message {
            background: #ffe8e8;
            color: #d9534f;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #d9534f;
        }
        .payment-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .feature-item i {
            font-size: 24px;
            color: #5f27cd;
            margin-bottom: 8px;
        }
        @media (max-width: 768px) {
            .phonepe-container {
                margin: 20px;
                padding: 20px;
            }
            .detail-row {
                flex-direction: column;
                gap: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="phonepe-container">
            <!-- Header -->
            <div class="phonepe-header">
                <img src="https://www.phonepe.com/webstatic/6.8.0/images/phonepe-logo.svg" alt="PhonePe" class="phonepe-logo">
                <h2>Secure Payment with PhonePe</h2>
                <p class="text-muted">Complete your membership purchase securely</p>
            </div>

            <!-- Error Message -->
            <?php if (isset($error_message)): ?>
            <div class="error-message">
                <i class="fa fa-exclamation-triangle"></i>
                <strong>Payment Error:</strong> <?php echo htmlspecialchars($error_message); ?>
            </div>
            <?php endif; ?>

            <!-- Plan Details -->
            <div class="plan-details">
                <h4><i class="fa fa-crown"></i> <?php echo htmlspecialchars($plan_data->plan_name); ?></h4>
                
                <div class="detail-row">
                    <span>Plan Duration:</span>
                    <span><?php echo $plan_data->plan_duration; ?> Days</span>
                </div>
                
                <div class="detail-row">
                    <span>Profile Views:</span>
                    <span><?php echo $plan_data->profile; ?></span>
                </div>
                
                <div class="detail-row">
                    <span>Messages:</span>
                    <span><?php echo $plan_data->plan_msg; ?></span>
                </div>
                
                <div class="detail-row">
                    <span>Contacts:</span>
                    <span><?php echo $plan_data->plan_contacts; ?></span>
                </div>
                
                <div class="detail-row">
                    <span>Chat Feature:</span>
                    <span><?php echo $plan_data->chat == 'YES' ? 'Included' : 'Not Included'; ?></span>
                </div>
                
                <div class="detail-row">
                    <span><strong>Total Amount:</strong></span>
                    <span><strong><?php echo PhonePeUtils::formatAmount($plan_data->plan_amount); ?></strong></span>
                </div>
            </div>

            <!-- User Details -->
            <div class="plan-details">
                <h4><i class="fa fa-user"></i> Billing Details</h4>
                
                <div class="detail-row">
                    <span>Name:</span>
                    <span><?php echo htmlspecialchars($user_data->first_name . ' ' . $user_data->last_name); ?></span>
                </div>
                
                <div class="detail-row">
                    <span>Email:</span>
                    <span><?php echo htmlspecialchars($user_data->email); ?></span>
                </div>
                
                <div class="detail-row">
                    <span>Mobile:</span>
                    <span><?php echo htmlspecialchars($user_data->mobile); ?></span>
                </div>
                
                <div class="detail-row">
                    <span>Member ID:</span>
                    <span><?php echo htmlspecialchars($user_data->matri_id); ?></span>
                </div>
            </div>

            <!-- Payment Features -->
            <div class="payment-features">
                <div class="feature-item">
                    <i class="fa fa-shield-alt"></i>
                    <div>Secure Payment</div>
                </div>
                <div class="feature-item">
                    <i class="fa fa-mobile-alt"></i>
                    <div>UPI & Cards</div>
                </div>
                <div class="feature-item">
                    <i class="fa fa-clock"></i>
                    <div>Instant Activation</div>
                </div>
                <div class="feature-item">
                    <i class="fa fa-headset"></i>
                    <div>24/7 Support</div>
                </div>
            </div>

            <!-- Security Info -->
            <div class="security-info">
                <i class="fa fa-lock"></i>
                <strong>Secure Transaction:</strong> Your payment is processed securely through PhonePe's encrypted gateway. We do not store your payment information.
            </div>

            <!-- Payment Form -->
            <form method="POST" id="phonepe-payment-form">
                <button type="submit" name="initiate_payment" class="phonepe-btn" id="pay-btn">
                    <i class="fa fa-credit-card"></i>
                    Pay <?php echo PhonePeUtils::formatAmount($plan_data->plan_amount); ?> with PhonePe
                </button>
            </form>

            <!-- Back Link -->
            <div class="text-center" style="margin-top: 20px;">
                <a href="paymentOptions.php?pid=<?php echo $planid; ?>" class="btn btn-link">
                    <i class="fa fa-arrow-left"></i> Choose Different Payment Method
                </a>
            </div>

            <!-- Terms -->
            <div class="text-center" style="margin-top: 20px; font-size: 12px; color: #666;">
                By proceeding, you agree to our <a href="#" target="_blank">Terms of Service</a> and <a href="#" target="_blank">Privacy Policy</a>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/jquery.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/icon-loader.js"></script>
    <script>
        $(document).ready(function() {
            // Disable button on form submission to prevent double submission
            $('#phonepe-payment-form').on('submit', function() {
                $('#pay-btn').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Processing Payment...');
            });
            
            // Validate mobile number
            var mobile = '<?php echo $user_data->mobile; ?>';
            if (!mobile || mobile.length < 10) {
                $('#pay-btn').prop('disabled', true).html('<i class="fa fa-exclamation-triangle"></i> Invalid Mobile Number');
                $('.phonepe-container').prepend('<div class="error-message"><i class="fa fa-exclamation-triangle"></i> <strong>Error:</strong> Please update your profile with a valid mobile number before making payment.</div>');
            }
        });
    </script>
</body>
</html>
