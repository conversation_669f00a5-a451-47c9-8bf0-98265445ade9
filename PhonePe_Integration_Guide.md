# PhonePe Payment Gateway Integration Guide
## Matrimony Website - Complete Integration

### 🎯 Overview
This guide covers the complete PhonePe payment gateway integration for your matrimony website, including setup, configuration, testing, and going live.

---

## 📁 Files Created

### **Core Integration Files:**
1. **`phonepe_integration.php`** - Main PhonePe payment class with all functionality
2. **`phonepe_payment_form.php`** - User-facing payment form
3. **`phonepe_callback.php`** - Handles payment response from PhonePe
4. **`phonepe_webhook.php`** - Server-to-server webhook handler

### **Admin & Configuration:**
5. **`premium_admin/PhonePeSettings.php`** - Admin panel for PhonePe configuration
6. **`phonepe_database_setup.sql`** - Database setup script
7. **`test_phonepe_integration.php`** - Integration test page

### **Updated Files:**
8. **`paymentOptions.php`** - Added PhonePe payment option

---

## 🚀 Setup Instructions

### **Step 1: Database Setup**
Run the SQL commands from `phonepe_database_setup.sql`:

```sql
-- Add PhonePe to payment methods
INSERT IGNORE INTO payment_method (pay_id, pay_name, status) VALUES ('4', 'PhonePe', 'PENDING');

-- Add PhonePe configuration columns
ALTER TABLE payment_method 
ADD COLUMN IF NOT EXISTS phonepe_merchant_id VARCHAR(255) DEFAULT '',
ADD COLUMN IF NOT EXISTS phonepe_salt_key TEXT DEFAULT '',
ADD COLUMN IF NOT EXISTS phonepe_salt_index INT DEFAULT 1,
ADD COLUMN IF NOT EXISTS phonepe_is_production ENUM('YES', 'NO') DEFAULT 'NO';

-- Update payments table for longer transaction IDs
ALTER TABLE payments MODIFY COLUMN pay_id VARCHAR(100);
```

### **Step 2: PhonePe Account Setup**
1. Visit [PhonePe Developer Portal](https://developer.phonepe.com)
2. Create a merchant account
3. Complete KYC verification
4. Get your credentials:
   - **Merchant ID**
   - **Salt Key** 
   - **Salt Index** (usually 1)

### **Step 3: Configure PhonePe**
1. Go to `premium_admin/PhonePeSettings.php`
2. Enter your PhonePe credentials
3. Set environment (Sandbox for testing, Production for live)
4. Enable PhonePe payments
5. Test the integration

### **Step 4: Webhook Configuration**
Configure these URLs in your PhonePe merchant dashboard:
- **Redirect URL:** `https://yourdomain.com/phonepe_callback.php`
- **Webhook URL:** `https://yourdomain.com/phonepe_webhook.php`

---

## 🔧 Configuration Details

### **Environment Settings:**

#### **Sandbox (Testing):**
- **API URL:** `https://api-preprod.phonepe.com/apis/hermes/pg/v1/pay`
- **Purpose:** Testing and development
- **Real Money:** No
- **Test Cards:** Available

#### **Production (Live):**
- **API URL:** `https://api.phonepe.com/apis/hermes/pg/v1/pay`
- **Purpose:** Live transactions
- **Real Money:** Yes
- **Approval:** Required from PhonePe

### **Required Credentials:**
```php
$merchant_id = "YOUR_MERCHANT_ID";        // From PhonePe dashboard
$salt_key = "YOUR_SALT_KEY";              // Secret key from PhonePe
$salt_index = 1;                          // Usually 1
$is_production = false;                   // true for live, false for testing
```

---

## 💳 Payment Flow

### **User Journey:**
1. **Select Plan** → User chooses membership plan
2. **Payment Options** → User sees PhonePe option
3. **PhonePe Form** → User confirms payment details
4. **PhonePe Gateway** → User completes payment
5. **Callback** → User redirected back with status
6. **Verification** → Server verifies payment with PhonePe
7. **Activation** → Membership activated if successful

### **Technical Flow:**
```
User → paymentOptions.php → phonepe_payment_form.php → PhonePe Gateway
                                                            ↓
Database ← phonepe_callback.php ← phonepe_webhook.php ← PhonePe Response
```

---

## 🔒 Security Features

### **Implemented Security:**
- **SHA256 Checksum:** All requests signed with SHA256
- **Signature Verification:** Webhook signatures validated
- **HTTPS Only:** All communication over HTTPS
- **Transaction Verification:** Server-side status verification
- **Session Security:** Secure session management
- **Data Validation:** Input validation and sanitization

### **Checksum Generation:**
```php
$string = $payload . '/pg/v1/pay' . $salt_key;
$checksum = hash('sha256', $string) . '###' . $salt_index;
```

---

## 🧪 Testing

### **Test Checklist:**
- ☐ Database tables updated
- ☐ PhonePe credentials configured
- ☐ Sandbox mode enabled
- ☐ SSL certificate installed
- ☐ Webhook URLs configured
- ☐ Test successful payment
- ☐ Test failed payment
- ☐ Test cancelled payment
- ☐ Verify webhook notifications
- ☐ Check database updates

### **Test Scenarios:**
1. **Successful Payment:** Complete payment and verify membership activation
2. **Failed Payment:** Test with invalid card and verify error handling
3. **Cancelled Payment:** Cancel payment and verify no charges
4. **Webhook Testing:** Verify webhook notifications are received
5. **Database Verification:** Check payment records are created correctly

---

## 📊 Admin Panel Features

### **PhonePe Settings Page:**
- **Credential Management:** Secure storage of API credentials
- **Environment Toggle:** Switch between Sandbox and Production
- **Status Control:** Enable/disable PhonePe payments
- **Test Integration:** Built-in testing functionality
- **Webhook URLs:** Display webhook URLs for configuration

### **Configuration Options:**
```php
// Admin can configure:
- Merchant ID
- Salt Key
- Salt Index
- Environment (Sandbox/Production)
- Payment Status (Enabled/Disabled)
```

---

## 🔍 Debugging & Logging

### **Log Files:**
- **Transaction Log:** `logs/phonepe_transactions.log`
- **Error Log:** PHP error logs
- **Webhook Log:** Webhook request/response logs

### **Debug Information:**
```php
// Enable debugging
PhonePeUtils::logTransaction($transaction_id, [
    'action' => 'payment_initiated',
    'user_id' => $user_id,
    'amount' => $amount,
    'response' => $api_response
]);
```

---

## 🚨 Troubleshooting

### **Common Issues:**

#### **1. Payment Fails with "Invalid Signature"**
- **Cause:** Incorrect Salt Key or Salt Index
- **Solution:** Verify credentials in admin panel

#### **2. Webhook Not Received**
- **Cause:** Incorrect webhook URL or SSL issues
- **Solution:** Check webhook URL configuration and SSL certificate

#### **3. "Merchant Not Found" Error**
- **Cause:** Incorrect Merchant ID or not approved
- **Solution:** Verify Merchant ID and account status

#### **4. Payment Success but Database Not Updated**
- **Cause:** Callback/webhook processing error
- **Solution:** Check logs and verify database permissions

---

## 📈 Going Live

### **Pre-Live Checklist:**
- ☐ Complete testing in sandbox
- ☐ PhonePe account approved for production
- ☐ SSL certificate installed and valid
- ☐ Webhook URLs configured correctly
- ☐ Production credentials configured
- ☐ Environment switched to Production
- ☐ Final end-to-end testing

### **Production Configuration:**
```php
// In admin panel, set:
Environment: Production
Merchant ID: [Production Merchant ID]
Salt Key: [Production Salt Key]
Status: Enabled
```

---

## 📞 Support & Resources

### **PhonePe Resources:**
- **API Documentation:** https://developer.phonepe.com/v1/docs/introduction-pg
- **Integration Guide:** https://developer.phonepe.com/v1/docs/payment-gateway-integration
- **Webhook Guide:** https://developer.phonepe.com/v1/docs/webhooks
- **Support Email:** <EMAIL>

### **Integration Files:**
- **Test Page:** `test_phonepe_integration.php`
- **Admin Config:** `premium_admin/PhonePeSettings.php`
- **Payment Form:** `phonepe_payment_form.php`
- **Core Class:** `phonepe_integration.php`

---

## ✅ Integration Complete

Your PhonePe payment gateway integration is now complete and ready for use. The integration includes:

- ✅ **Complete Payment Flow** - From plan selection to membership activation
- ✅ **Admin Configuration** - Easy setup through admin panel
- ✅ **Security Features** - SHA256 signatures and validation
- ✅ **Webhook Support** - Real-time payment notifications
- ✅ **Error Handling** - Comprehensive error management
- ✅ **Logging & Debug** - Full transaction logging
- ✅ **Testing Tools** - Built-in testing capabilities
- ✅ **Documentation** - Complete setup and usage guide

**Next Steps:**
1. Configure your PhonePe credentials
2. Test in sandbox mode
3. Configure webhooks
4. Go live when ready

---

**Integration Date:** $(date)  
**Status:** ✅ Complete  
**Ready for:** Configuration and Testing  
**Production Ready:** After testing and approval
