/* Icon Fixes for Matrimony Site */

/* ========================================
   GLYPHICON REPLACEMENT WITH BOOTSTRAP ICONS
   ======================================== */

/* Replace missing Glyphicons with Bootstrap Icons */
.gi, .glyphicon {
    font-family: "bootstrap-icons" !important;
    font-style: normal;
    font-weight: normal;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Common Glyphicon replacements */
.gi-user:before, .glyphicon-user:before { content: "\f4da"; }
.gi-envelope:before, .glyphicon-envelope:before { content: "\f32f"; }
.gi-lock:before, .glyphicon-lock:before { content: "\f3f1"; }
.gi-phone:before, .glyphicon-phone:before { content: "\f4b2"; }
.gi-calendar:before, .glyphicon-calendar:before { content: "\f1ec"; }
.gi-heart:before, .glyphicon-heart:before { content: "\f33e"; }
.gi-search:before, .glyphicon-search:before { content: "\f52a"; }
.gi-home:before, .glyphicon-home:before { content: "\f32d"; }
.gi-star:before, .glyphicon-star:before { content: "\f588"; }
.gi-star-empty:before, .glyphicon-star-empty:before { content: "\f586"; }
.gi-edit:before, .glyphicon-edit:before { content: "\f4ca"; }
.gi-trash:before, .glyphicon-trash:before { content: "\f5de"; }
.gi-plus:before, .glyphicon-plus:before { content: "\f4fe"; }
.gi-minus:before, .glyphicon-minus:before { content: "\f3f8"; }
.gi-ok:before, .glyphicon-ok:before { content: "\f26a"; }
.gi-remove:before, .glyphicon-remove:before { content: "\f659"; }
.gi-refresh:before, .glyphicon-refresh:before { content: "\f3f7"; }
.gi-download:before, .glyphicon-download:before { content: "\f2c6"; }
.gi-upload:before, .glyphicon-upload:before { content: "\f2c7"; }
.gi-camera:before, .glyphicon-camera:before { content: "\f1f5"; }
.gi-picture:before, .glyphicon-picture:before { content: "\f4b6"; }
.gi-map-marker:before, .glyphicon-map-marker:before { content: "\f3c5"; }
.gi-time:before, .glyphicon-time:before { content: "\f292"; }
.gi-eye-open:before, .glyphicon-eye-open:before { content: "\f341"; }
.gi-eye-close:before, .glyphicon-eye-close:before { content: "\f33f"; }
.gi-warning-sign:before, .glyphicon-warning-sign:before { content: "\f33a"; }
.gi-info-sign:before, .glyphicon-info-sign:before { content: "\f431"; }
.gi-question-sign:before, .glyphicon-question-sign:before { content: "\f4a6"; }
.gi-exclamation-sign:before, .glyphicon-exclamation-sign:before { content: "\f33a"; }

/* Loading and spinner icons */
.gi-loader:before, .glyphicon-loader:before { content: "\f3f7"; }
.gi-spin, .glyphicon-spin {
    animation: gi-spin 1s linear infinite;
}

@keyframes gi-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ========================================
   FONT AWESOME ICON FIXES
   ======================================== */

/* Fix for Font Awesome 6 compatibility */
.fa-mobile-alt:before { content: "\f3cd"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-user-circle:before { content: "\f2bd"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-walking:before { content: "\f554"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-graduation-cap:before { content: "\f19d"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-briefcase:before { content: "\f0b1"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-home:before { content: "\f015"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-heart:before { content: "\f004"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-envelope:before { content: "\f0e0"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-phone:before { content: "\f095"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-camera:before { content: "\f030"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-upload:before { content: "\f093"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-download:before { content: "\f019"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-search:before { content: "\f002"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-filter:before { content: "\f0b0"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-cog:before { content: "\f013"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-edit:before { content: "\f044"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-trash:before { content: "\f1f8"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-plus:before { content: "\f067"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-minus:before { content: "\f068"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-check:before { content: "\f00c"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-times:before { content: "\f00d"; font-family: "Font Awesome 6 Free"; font-weight: 900; }

/* PhonePe specific icons */
.fa-crown:before { content: "\f521"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-user:before { content: "\f007"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-shield-alt:before { content: "\f3ed"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-mobile-alt:before { content: "\f3cd"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-clock:before { content: "\f017"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-headset:before { content: "\f590"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-lock:before { content: "\f023"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-credit-card:before { content: "\f09d"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-arrow-left:before { content: "\f060"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-spinner:before { content: "\f110"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-exclamation-triangle:before { content: "\f071"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-check-circle:before { content: "\f058"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-times-circle:before { content: "\f057"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-info-circle:before { content: "\f05a"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-refresh:before { content: "\f021"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-retry:before { content: "\f021"; font-family: "Font Awesome 6 Free"; font-weight: 900; }

/* Payment and financial icons */
.fa-rupee-sign:before { content: "\f156"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-money-bill:before { content: "\f0d6"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-wallet:before { content: "\f555"; font-family: "Font Awesome 6 Free"; font-weight: 900; }
.fa-receipt:before { content: "\f543"; font-family: "Font Awesome 6 Free"; font-weight: 900; }

/* Animation support */
.fa-spin {
    animation: fa-spin 1s linear infinite;
}

@keyframes fa-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Social media icons - Font Awesome 6 Brand Icons */
.fa-facebook:before, .fab.fa-facebook:before { content: "\f09a"; font-family: "Font Awesome 6 Brands"; font-weight: 400; }
.fa-facebook-square:before, .fab.fa-facebook-square:before { content: "\f082"; font-family: "Font Awesome 6 Brands"; font-weight: 400; }
.fa-twitter:before, .fab.fa-twitter:before { content: "\f099"; font-family: "Font Awesome 6 Brands"; font-weight: 400; }
.fa-twitter-square:before, .fab.fa-twitter-square:before { content: "\f081"; font-family: "Font Awesome 6 Brands"; font-weight: 400; }
.fa-instagram:before, .fab.fa-instagram:before { content: "\f16d"; font-family: "Font Awesome 6 Brands"; font-weight: 400; }
.fa-linkedin:before, .fab.fa-linkedin:before { content: "\f08c"; font-family: "Font Awesome 6 Brands"; font-weight: 400; }
.fa-linkedin-square:before, .fab.fa-linkedin-square:before { content: "\f08c"; font-family: "Font Awesome 6 Brands"; font-weight: 400; }
.fa-pinterest:before, .fab.fa-pinterest:before { content: "\f0d2"; font-family: "Font Awesome 6 Brands"; font-weight: 400; }
.fa-pinterest-square:before, .fab.fa-pinterest-square:before { content: "\f0d3"; font-family: "Font Awesome 6 Brands"; font-weight: 400; }
.fa-youtube:before, .fab.fa-youtube:before { content: "\f167"; font-family: "Font Awesome 6 Brands"; font-weight: 400; }
.fa-whatsapp:before, .fab.fa-whatsapp:before { content: "\f232"; font-family: "Font Awesome 6 Brands"; font-weight: 400; }
.fa-google:before, .fab.fa-google:before { content: "\f1a0"; font-family: "Font Awesome 6 Brands"; font-weight: 400; }
.fa-google-plus:before, .fab.fa-google-plus:before { content: "\f2b3"; font-family: "Font Awesome 6 Brands"; font-weight: 400; }

/* Font Awesome Brand Class Fix */
.fab {
    font-family: "Font Awesome 6 Brands" !important;
    font-weight: 400 !important;
    font-style: normal;
}

/* Ensure all brand icons use correct font family */
.fab.fa-facebook-square,
.fab.fa-twitter-square,
.fab.fa-linkedin,
.fab.fa-pinterest-square {
    font-family: "Font Awesome 6 Brands" !important;
    font-weight: 400 !important;
}

/* Legacy support for old class names */
.fa.fa-facebook-square { font-family: "Font Awesome 6 Brands" !important; font-weight: 400 !important; }
.fa.fa-twitter-square { font-family: "Font Awesome 6 Brands" !important; font-weight: 400 !important; }
.fa.fa-linkedin { font-family: "Font Awesome 6 Brands" !important; font-weight: 400 !important; }
.fa.fa-pinterest-square { font-family: "Font Awesome 6 Brands" !important; font-weight: 400 !important; }

/* ========================================
   FONT AWESOME FALLBACK SYSTEM
   ======================================== */

/* Ensure Font Awesome icons load correctly */
.fa, .fas, .far, .fal, .fab {
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Brand icons specific font family */
.fab {
    font-family: "Font Awesome 6 Brands" !important;
    font-weight: 400 !important;
}

/* Light icons */
.fal {
    font-weight: 300 !important;
}

/* Regular icons */
.far {
    font-weight: 400 !important;
}

/* Solid icons (default) */
.fas, .fa {
    font-weight: 900 !important;
}

/* Force Font Awesome to load if kit fails */
@font-face {
    font-family: "Font Awesome 6 Free";
    src: url("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-solid-900.woff2") format("woff2");
    font-weight: 900;
    font-style: normal;
}

@font-face {
    font-family: "Font Awesome 6 Brands";
    src: url("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-brands-400.woff2") format("woff2");
    font-weight: 400;
    font-style: normal;
}

/* ========================================
   CUSTOM ICON STYLES
   ======================================== */

/* Registration form icons */
.gtRegTitle i {
    color: #e47203;
    margin-right: 10px;
    font-size: 1.2em;
}

/* Form field icons */
.input-group-addon i {
    color: #499202;
    font-size: 16px;
}

/* Button icons */
.btn i {
    margin-right: 8px;
}

/* Panel header icons */
.gt-panel-head i {
    margin-right: 8px;
    color: inherit;
}

/* Navigation icons */
.navbar-nav i {
    margin-right: 5px;
}

/* Footer social icons */
.gt-footer-social i {
    font-size: 24px;
    transition: all 0.3s ease;
}

.gt-footer-social i:hover {
    transform: scale(1.1);
}

/* ========================================
   ICON SIZE CLASSES
   ======================================== */

.icon-xs { font-size: 0.75em; }
.icon-sm { font-size: 0.875em; }
.icon-lg { font-size: 1.25em; }
.icon-xl { font-size: 1.5em; }
.icon-2x { font-size: 2em; }
.icon-3x { font-size: 3em; }
.icon-4x { font-size: 4em; }
.icon-5x { font-size: 5em; }

/* ========================================
   ICON ANIMATIONS
   ======================================== */

.icon-spin {
    animation: icon-spin 1s linear infinite;
}

.icon-pulse {
    animation: icon-pulse 1s ease-in-out infinite alternate;
}

.icon-bounce {
    animation: icon-bounce 1s ease-in-out infinite;
}

@keyframes icon-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes icon-pulse {
    0% { opacity: 1; }
    100% { opacity: 0.5; }
}

@keyframes icon-bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* ========================================
   ICON COLOR THEMES
   ======================================== */

.icon-primary { color: #499202; }
.icon-secondary { color: #e47203; }
.icon-success { color: #5cb85c; }
.icon-info { color: #5bc0de; }
.icon-warning { color: #f0ad4e; }
.icon-danger { color: #d9534f; }
.icon-muted { color: #777; }
.icon-white { color: #fff; }

/* ========================================
   RESPONSIVE ICON ADJUSTMENTS
   ======================================== */

@media (max-width: 767px) {
    .gtRegTitle i {
        font-size: 1.1em;
        margin-right: 8px;
    }
    
    .gt-footer-social i {
        font-size: 20px;
    }
    
    .btn i {
        margin-right: 6px;
    }
}

@media (max-width: 480px) {
    .gtRegTitle i {
        font-size: 1em;
        margin-right: 6px;
    }
    
    .gt-footer-social i {
        font-size: 18px;
    }
}
