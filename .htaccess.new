# ========================================
# MATRIMONY SITE .HTACCESS CONFIGURATION
# ========================================

# php -- BEGIN cPanel-generated handler, do not edit
# This domain inherits the "PHP" package.
# php -- END cPanel-generated handler, do not edit

# ========================================
# SECURITY HEADERS
# ========================================
<IfModule mod_headers.c>
    # Prevent clickjacking
    Header always append X-Frame-Options SAMEORIGIN
    
    # Prevent MIME type sniffing
    Header set X-Content-Type-Options nosniff
    
    # Enable XSS protection
    Header set X-XSS-Protection "1; mode=block"
    
    # Referrer Policy
    Header set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Remove server signature
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# ========================================
# URL REWRITING
# ========================================
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Block access to sensitive files
    RewriteRule ^(\.htaccess|\.htpasswd|\.env|config\.php|dbConf\.php)$ - [F,L]
    
    # Block access to backup and log files
    RewriteRule \.(bak|backup|log|sql|txt)$ - [F,L]
    
    # Redirect www to non-www (only for production)
    # RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
    # RewriteRule ^(.*)$ https://%1/$1 [R=301,L]
    
    # Force HTTPS (only for production - uncomment when live)
    # RewriteCond %{HTTPS} off
    # RewriteCond %{HTTP_HOST} !^localhost [NC]
    # RewriteCond %{HTTP_HOST} !^127\.0\.0\.1 [NC]
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
    
    # Clean URLs - Remove .php extension
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME}.php -f
    RewriteRule ^(.*)$ $1.php [L,QSA]
    
    # Remove trailing slash from URLs
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [R=301,L]
    
    # Redirect old URLs to new structure (if needed)
    # RewriteRule ^old-page$ new-page [R=301,L]
</IfModule>

# ========================================
# PERFORMANCE OPTIMIZATION
# ========================================

# Enable Gzip Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Browser Caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# ========================================
# ERROR HANDLING
# ========================================
# ErrorDocument 404 /404.php
# ErrorDocument 403 /403.php
# ErrorDocument 500 /500.php

# ========================================
# PHP SETTINGS
# ========================================
<IfModule mod_php7.c>
    # Increase memory limit for large operations
    php_value memory_limit 256M
    
    # Increase upload limits
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    
    # Session settings
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 0
    
    # Hide PHP version
    php_flag expose_php off
</IfModule>

# ========================================
# DIRECTORY PROTECTION
# ========================================
Options -Indexes
Options -ExecCGI

# Protect important directories
<Files "*.inc">
    Order allow,deny
    Deny from all
</Files>
