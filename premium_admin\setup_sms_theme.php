<?php
/**
 * Setup Script for SMS and Theme Settings
 * Run this script once to initialize the SMS and theme settings tables
 */

include_once '../databaseConn.php';
$DatabaseCo = new DatabaseConn();

$messages = [];
$errors = [];

echo "<!DOCTYPE html>
<html>
<head>
    <title>Setup SMS & Theme Settings</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .success { color: green; background: #e8f5e8; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .error { color: red; background: #ffe8e8; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .info { color: blue; background: #e8f0ff; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>";

echo "<h1>🚀 SMS & Theme Settings Setup</h1>";
echo "<p>This script will initialize the SMS gateway and theme customization features.</p>";

// Step 1: Create SMS Settings Table
echo "<div class='step'>";
echo "<h2>Step 1: Creating SMS Settings Table</h2>";

$sms_table_sql = "CREATE TABLE IF NOT EXISTS `sms_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `provider` varchar(50) NOT NULL DEFAULT 'fast2sms',
  `fast2sms_api_key` varchar(255) DEFAULT NULL,
  `fast2sms_sender_id` varchar(10) DEFAULT NULL,
  `fast2sms_route` varchar(20) DEFAULT 'otp',
  `msg91_api_key` varchar(255) DEFAULT NULL,
  `msg91_sender_id` varchar(10) DEFAULT NULL,
  `textlocal_api_key` varchar(255) DEFAULT NULL,
  `textlocal_sender_id` varchar(10) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `test_mobile` varchar(15) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

if ($DatabaseCo->dbLink->query($sms_table_sql)) {
    echo "<div class='success'>✅ SMS settings table created successfully</div>";
    
    // Insert default SMS settings
    $check_sms = $DatabaseCo->dbLink->query("SELECT COUNT(*) as count FROM sms_settings");
    $sms_count = mysqli_fetch_object($check_sms);
    
    if ($sms_count->count == 0) {
        $insert_sms = "INSERT INTO sms_settings (provider, fast2sms_api_key, fast2sms_sender_id, fast2sms_route) 
                       VALUES ('fast2sms', 'r2oeVHLQzpadwlNAGiEXtBfguK1IWS7YxZ58ROqn3DhMFvy9UbjN4C6iqf59nFPW8yD0wuZslmAJRtOr', 'FSTSMS', 'otp')";
        
        if ($DatabaseCo->dbLink->query($insert_sms)) {
            echo "<div class='success'>✅ Default SMS settings inserted</div>";
        } else {
            echo "<div class='error'>❌ Error inserting default SMS settings: " . mysqli_error($DatabaseCo->dbLink) . "</div>";
        }
    } else {
        echo "<div class='info'>ℹ️ SMS settings already exist</div>";
    }
} else {
    echo "<div class='error'>❌ Error creating SMS settings table: " . mysqli_error($DatabaseCo->dbLink) . "</div>";
}
echo "</div>";

// Step 2: Create Theme Settings Table
echo "<div class='step'>";
echo "<h2>Step 2: Creating Theme Settings Table</h2>";

$theme_table_sql = "CREATE TABLE IF NOT EXISTS `theme_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `primary_color` varchar(7) DEFAULT '#499202',
  `secondary_color` varchar(7) DEFAULT '#e47203',
  `accent_color` varchar(7) DEFAULT '#089cbe',
  `success_color` varchar(7) DEFAULT '#00a65a',
  `warning_color` varchar(7) DEFAULT '#f39c12',
  `danger_color` varchar(7) DEFAULT '#f56954',
  `dark_color` varchar(7) DEFAULT '#222222',
  `light_color` varchar(7) DEFAULT '#f4f4f4',
  `button_primary_color` varchar(7) DEFAULT '#499202',
  `button_secondary_color` varchar(7) DEFAULT '#e47203',
  `link_color` varchar(7) DEFAULT '#089cbe',
  `link_hover_color` varchar(7) DEFAULT '#067a9b',
  `header_bg_color` varchar(7) DEFAULT '#ffffff',
  `footer_bg_color` varchar(7) DEFAULT '#2c3e50',
  `sidebar_bg_color` varchar(7) DEFAULT '#f8f9fa',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

if ($DatabaseCo->dbLink->query($theme_table_sql)) {
    echo "<div class='success'>✅ Theme settings table created successfully</div>";
    
    // Insert default theme settings
    $check_theme = $DatabaseCo->dbLink->query("SELECT COUNT(*) as count FROM theme_settings");
    $theme_count = mysqli_fetch_object($check_theme);
    
    if ($theme_count->count == 0) {
        $insert_theme = "INSERT INTO theme_settings (id) VALUES (1)";
        
        if ($DatabaseCo->dbLink->query($insert_theme)) {
            echo "<div class='success'>✅ Default theme settings inserted</div>";
        } else {
            echo "<div class='error'>❌ Error inserting default theme settings: " . mysqli_error($DatabaseCo->dbLink) . "</div>";
        }
    } else {
        echo "<div class='info'>ℹ️ Theme settings already exist</div>";
    }
} else {
    echo "<div class='error'>❌ Error creating theme settings table: " . mysqli_error($DatabaseCo->dbLink) . "</div>";
}
echo "</div>";

// Step 3: Update mobile-apis.php
echo "<div class='step'>";
echo "<h2>Step 3: Updating mobile-apis.php</h2>";

$mobile_apis_content = "<?php
// Auto-generated SMS API configuration
// Last updated: " . date('Y-m-d H:i:s') . "
// This file is automatically updated when SMS settings are changed in admin panel

// Get SMS settings from database
include_once 'databaseConn.php';
\$DatabaseCo = new DatabaseConn();
\$sms_sql = \$DatabaseCo->dbLink->query(\"SELECT * FROM sms_settings WHERE id='1'\");

if (\$sms_sql && mysqli_num_rows(\$sms_sql) > 0) {
    \$sms_settings = mysqli_fetch_object(\$sms_sql);
    
    switch (\$sms_settings->provider) {
        case 'fast2sms':
            if (\$sms_settings->fast2sms_route == 'otp') {
                \$url = \"https://www.fast2sms.com/dev/bulkV2?authorization={\$sms_settings->fast2sms_api_key}&route=otp&variables_values=\$order_id&flash=0&numbers=\$mno\";
            } else {
                \$url = \"https://www.fast2sms.com/dev/bulkV2?authorization={\$sms_settings->fast2sms_api_key}&route=dlt&sender_id={\$sms_settings->fast2sms_sender_id}&message=Your%20OTP%20is%20\$order_id.%20Do%20not%20share%20with%20anyone.&variables_values=\$order_id&flash=0&numbers=\$mno\";
            }
            break;
        case 'msg91':
            \$url = \"https://control.msg91.com/api/sendhttp.php?authkey={\$sms_settings->msg91_api_key}&mobiles=\$mno&message=Your%20OTP%20is%20\$order_id.%20Do%20not%20share%20with%20anyone.&sender={\$sms_settings->msg91_sender_id}&route=4&country=91\";
            break;
        case 'textlocal':
            \$url = \"https://api.textlocal.in/send/?apikey={\$sms_settings->textlocal_api_key}&numbers=\$mno&message=Your%20OTP%20is%20\$order_id.%20Do%20not%20share%20with%20anyone.&sender={\$sms_settings->textlocal_sender_id}\";
            break;
        default:
            // Fallback to original Fast2SMS configuration
            \$url = \"https://www.fast2sms.com/dev/bulkV2?authorization=r2oeVHLQzpadwlNAGiEXtBfguK1IWS7YxZ58ROqn3DhMFvy9UbjN4C6iqf59nFPW8yD0wuZslmAJRtOr&route=otp&variables_values=\$order_id&flash=0&numbers=\$mno\";
    }
} else {
    // Fallback to original configuration
    \$url = \"https://www.fast2sms.com/dev/bulkV2?authorization=r2oeVHLQzpadwlNAGiEXtBfguK1IWS7YxZ58ROqn3DhMFvy9UbjN4C6iqf59nFPW8yD0wuZslmAJRtOr&route=otp&variables_values=\$order_id&flash=0&numbers=\$mno\";
}
?>";

if (file_put_contents('../mobile-apis.php', $mobile_apis_content)) {
    echo "<div class='success'>✅ mobile-apis.php updated successfully</div>";
} else {
    echo "<div class='error'>❌ Error updating mobile-apis.php</div>";
}
echo "</div>";

// Step 4: Create initial theme CSS
echo "<div class='step'>";
echo "<h2>Step 4: Creating Initial Theme CSS</h2>";

$initial_css = "/* Auto-generated theme CSS - Last updated: " . date('Y-m-d H:i:s') . " */

:root {
  --primary-color: #499202;
  --secondary-color: #e47203;
  --accent-color: #089cbe;
  --success-color: #00a65a;
  --warning-color: #f39c12;
  --danger-color: #f56954;
  --dark-color: #222222;
  --light-color: #f4f4f4;
}

/* Primary Theme Colors */
.inThemeGreen, .gt-text-green { color: #499202 !important; }
.inThemeOrange, .gt-text-orange { color: #e47203 !important; }
.gt-text-blue { color: #089cbe !important; }

/* Background Colors */
.gt-bg-green { background: #499202 !important; }
.gt-bg-orange { background: #e47203 !important; }
.gt-bg-blue { background: #089cbe !important; }

/* Button Colors */
.btn.gt-btn-green, .gt-btn-green { background-color: #499202 !important; border-color: #499202 !important; }
.btn.gt-btn-orange, .gt-btn-orange { background-color: #e47203 !important; border-color: #e47203 !important; }
.btn.gt-btn-green:hover { background-color: #3a7302 !important; }
.btn.gt-btn-orange:hover { background-color: #c55a02 !important; }

/* Link Colors */
a { color: #089cbe; }
a:hover, a:focus { color: #067a9b; }";

if (file_put_contents('../css/theme-custom.css', $initial_css)) {
    echo "<div class='success'>✅ Initial theme CSS created successfully</div>";
} else {
    echo "<div class='error'>❌ Error creating initial theme CSS</div>";
}
echo "</div>";

// Final Summary
echo "<div class='step'>";
echo "<h2>🎉 Setup Complete!</h2>";
echo "<div class='success'>";
echo "<h3>What's been set up:</h3>";
echo "<ul>";
echo "<li>✅ SMS Settings table with Fast2SMS default configuration</li>";
echo "<li>✅ Theme Settings table with default color scheme</li>";
echo "<li>✅ Updated mobile-apis.php to use database settings</li>";
echo "<li>✅ Created initial theme CSS file</li>";
echo "<li>✅ Added new menu items to admin panel</li>";
echo "</ul>";

echo "<h3>Next Steps:</h3>";
echo "<ol>";
echo "<li>Go to <strong>Admin Panel → Site Settings → SMS Gateway Settings</strong> to configure your SMS provider</li>";
echo "<li>Go to <strong>Admin Panel → Site Settings → Theme & Color Settings</strong> to customize colors</li>";
echo "<li>Test SMS functionality using the test feature in SMS settings</li>";
echo "<li>Include the theme CSS in your site templates by adding: <code>&lt;?php include 'parts/theme_css.php'; ?&gt;</code></li>";
echo "</ol>";
echo "</div>";
echo "</div>";

echo "<p><a href='dashboard.php'>← Back to Admin Dashboard</a></p>";

echo "</body></html>";
?>
