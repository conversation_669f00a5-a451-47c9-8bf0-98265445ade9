<?php
/**
 * PhonePe Dashboard Widget
 * Shows PhonePe status in admin dashboard
 */

include_once '../databaseConn.php';
include_once '../phonepe_integration.php';

$DatabaseCo = new DatabaseConn();

// Get PhonePe configuration
$phonepe_config = PhonePeConfig::getConfig($DatabaseCo->dbLink);

// Get payment statistics
$total_payments = 0;
$phonepe_payments = 0;
$phonepe_revenue = 0;

if ($phonepe_config) {
    // Get total payments
    $total_result = $DatabaseCo->dbLink->query("SELECT COUNT(*) as count FROM payments");
    if ($total_result) {
        $total_payments = mysqli_fetch_object($total_result)->count;
    }
    
    // Get PhonePe payments (assuming pay_id contains transaction ID starting with specific prefix)
    $phonepe_result = $DatabaseCo->dbLink->query("SELECT COUNT(*) as count, SUM(CAST(SUBSTRING_INDEX(p_amount, ' ', -1) AS DECIMAL(10,2))) as revenue FROM payments WHERE paymode LIKE '%PhonePe%' OR p_bank_detail LIKE '%PhonePe%'");
    if ($phonepe_result) {
        $phonepe_data = mysqli_fetch_object($phonepe_result);
        $phonepe_payments = $phonepe_data->count ?: 0;
        $phonepe_revenue = $phonepe_data->revenue ?: 0;
    }
}
?>

<!-- PhonePe Dashboard Widget -->
<div class="col-lg-3 col-xs-6">
    <div class="small-box" style="background: linear-gradient(135deg, #5f27cd 0%, #7c4dff 100%); color: white;">
        <div class="inner">
            <h3><?php echo $phonepe_payments; ?></h3>
            <p>PhonePe Payments</p>
        </div>
        <div class="icon">
            <i class="fa fa-credit-card"></i>
        </div>
        <a href="PhonePeSettings.php" class="small-box-footer" style="color: white;">
            Configure PhonePe <i class="fa fa-arrow-circle-right"></i>
        </a>
    </div>
</div>

<!-- PhonePe Revenue Widget -->
<div class="col-lg-3 col-xs-6">
    <div class="small-box" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;">
        <div class="inner">
            <h3>₹<?php echo number_format($phonepe_revenue, 0); ?></h3>
            <p>PhonePe Revenue</p>
        </div>
        <div class="icon">
            <i class="fa fa-rupee-sign"></i>
        </div>
        <a href="SalesReport.php" class="small-box-footer" style="color: white;">
            View Reports <i class="fa fa-arrow-circle-right"></i>
        </a>
    </div>
</div>

<!-- PhonePe Status Widget -->
<div class="col-lg-6 col-xs-12">
    <div class="box box-primary">
        <div class="box-header with-border">
            <h3 class="box-title">
                <i class="fa fa-mobile-alt"></i> PhonePe Payment Gateway Status
            </h3>
            <div class="box-tools pull-right">
                <a href="PhonePeSettings.php" class="btn btn-primary btn-xs">
                    <i class="fa fa-cog"></i> Configure
                </a>
            </div>
        </div>
        <div class="box-body">
            <?php if ($phonepe_config): ?>
            <div class="row">
                <div class="col-md-6">
                    <strong>Status:</strong>
                    <span class="label label-<?php echo $phonepe_config->status == 'APPROVED' ? 'success' : 'warning'; ?>">
                        <?php echo $phonepe_config->status == 'APPROVED' ? 'Active' : 'Inactive'; ?>
                    </span>
                </div>
                <div class="col-md-6">
                    <strong>Environment:</strong>
                    <span class="label label-<?php echo $phonepe_config->phonepe_is_production == 'YES' ? 'danger' : 'info'; ?>">
                        <?php echo $phonepe_config->phonepe_is_production == 'YES' ? 'Production' : 'Sandbox'; ?>
                    </span>
                </div>
            </div>
            <div class="row" style="margin-top: 10px;">
                <div class="col-md-6">
                    <strong>Merchant ID:</strong>
                    <?php echo $phonepe_config->phonepe_merchant_id ? substr($phonepe_config->phonepe_merchant_id, 0, 8) . '...' : 'Not Set'; ?>
                </div>
                <div class="col-md-6">
                    <strong>Total Transactions:</strong>
                    <?php echo $phonepe_payments; ?>
                </div>
            </div>
            
            <?php if ($phonepe_config->status == 'APPROVED'): ?>
            <div class="row" style="margin-top: 15px;">
                <div class="col-md-12">
                    <div class="btn-group btn-group-sm">
                        <a href="../test_phonepe_integration.php" target="_blank" class="btn btn-success">
                            <i class="fa fa-test-tube"></i> Test Integration
                        </a>
                        <a href="../phonepe_production_deployment.php" target="_blank" class="btn btn-warning">
                            <i class="fa fa-rocket"></i> Production Guide
                        </a>
                        <a href="PaymentOption.php" class="btn btn-info">
                            <i class="fa fa-cog"></i> Payment Settings
                        </a>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <?php else: ?>
            <div class="alert alert-warning" style="margin-bottom: 0;">
                <h5><i class="fa fa-exclamation-triangle"></i> PhonePe Not Configured</h5>
                <p style="margin-bottom: 10px;">PhonePe payment gateway is not configured yet.</p>
                <a href="PhonePeSettings.php" class="btn btn-primary btn-sm">
                    <i class="fa fa-plus"></i> Configure PhonePe Now
                </a>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
/* Custom styles for PhonePe widgets */
.phonepe-widget {
    background: linear-gradient(135deg, #5f27cd 0%, #7c4dff 100%);
    color: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.phonepe-widget .widget-icon {
    font-size: 36px;
    opacity: 0.8;
    float: right;
    margin-top: -10px;
}

.phonepe-widget h3 {
    margin: 0 0 5px 0;
    font-size: 28px;
    font-weight: 600;
}

.phonepe-widget p {
    margin: 0;
    opacity: 0.9;
}

.phonepe-widget a {
    color: white;
    text-decoration: none;
    display: block;
    margin-top: 10px;
    padding: 5px 0;
    border-top: 1px solid rgba(255,255,255,0.2);
}

.phonepe-widget a:hover {
    color: white;
    text-decoration: none;
    background: rgba(255,255,255,0.1);
    margin: 10px -15px 0 -15px;
    padding: 5px 15px;
}
</style>

<script>
$(document).ready(function() {
    // Auto-refresh PhonePe status every 30 seconds
    setInterval(function() {
        // You can add AJAX call here to refresh PhonePe status
        console.log('PhonePe status check...');
    }, 30000);
    
    // Add click tracking for PhonePe widgets
    $('.phonepe-widget a, .small-box a').on('click', function() {
        var link = $(this).attr('href');
        console.log('PhonePe widget clicked: ' + link);
    });
});
</script>
