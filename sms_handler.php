<?php
/**
 * Enhanced SMS Handler with Multiple Provider Support
 * This file provides fallback options for SMS sending
 */

class SMSHandler {

    private $sms_settings;
    private $debug_mode;
    private $db_connection;

    public function __construct($debug = false) {
        $this->debug_mode = $debug;
        $this->loadSMSSettings();
    }

    /**
     * Load SMS settings from database
     */
    private function loadSMSSettings() {
        // Include database connection
        include_once 'databaseConn.php';
        $DatabaseCo = new DatabaseConn();
        $this->db_connection = $DatabaseCo;

        // Get SMS settings from database
        $sql = $DatabaseCo->dbLink->query("SELECT * FROM sms_settings WHERE id='1'");
        if ($sql && mysqli_num_rows($sql) > 0) {
            $this->sms_settings = mysqli_fetch_object($sql);
        } else {
            // Fallback to default settings
            $this->sms_settings = (object) [
                'provider' => 'fast2sms',
                'fast2sms_api_key' => 'r2oeVHLQzpadwlNAGiEXtBfguK1IWS7YxZ58ROqn3DhMFvy9UbjN4C6iqf59nFPW8yD0wuZslmAJRtOr',
                'fast2sms_sender_id' => 'FSTSMS',
                'fast2sms_route' => 'otp',
                'msg91_api_key' => '',
                'msg91_sender_id' => '',
                'textlocal_api_key' => '',
                'textlocal_sender_id' => ''
            ];
        }
    }
    
    /**
     * Send OTP using configured provider
     */
    public function sendOTP($mobile, $otp, $message = null) {
        if (!$message) {
            $message = "Your OTP is $otp. Do not share with anyone.";
        }

        // Use the configured provider
        switch ($this->sms_settings->provider) {
            case 'fast2sms':
                $result = $this->sendViaFast2SMS($mobile, $otp, $message);
                break;
            case 'msg91':
                $result = $this->sendViaMSG91($mobile, $otp, $message);
                break;
            case 'textlocal':
                $result = $this->sendViaTextlocal($mobile, $otp, $message);
                break;
            default:
                $result = [
                    'success' => false,
                    'message' => 'Invalid SMS provider configured',
                    'provider' => 'Unknown',
                    'response' => ''
                ];
        }

        if (!$result['success']) {
            // Log the error for debugging
            $this->logError("SMS sending failed for mobile: $mobile, OTP: $otp", $result);
        }

        return $result;
    }
    
    /**
     * Send via Fast2SMS
     */
    private function sendViaFast2SMS($mobile, $otp, $message) {
        $api_key = $this->sms_settings->fast2sms_api_key;
        $sender_id = $this->sms_settings->fast2sms_sender_id;
        $route = $this->sms_settings->fast2sms_route;

        if ($route == 'otp') {
            $url = "https://www.fast2sms.com/dev/bulkV2?authorization=$api_key&route=otp&variables_values=$otp&flash=0&numbers=$mobile";
        } else {
            $encoded_message = urlencode($message);
            $url = "https://www.fast2sms.com/dev/bulkV2?authorization=$api_key&route=dlt&sender_id=$sender_id&message=$encoded_message&variables_values=$otp&flash=0&numbers=$mobile";
        }

        return $this->makeAPICall($url, 'Fast2SMS ' . ucfirst($route) . ' Route');
    }

    /**
     * Send via MSG91
     */
    private function sendViaMSG91($mobile, $otp, $message) {
        $api_key = $this->sms_settings->msg91_api_key;
        $sender_id = $this->sms_settings->msg91_sender_id;
        $encoded_message = urlencode($message);

        $url = "https://control.msg91.com/api/sendhttp.php?authkey=$api_key&mobiles=$mobile&message=$encoded_message&sender=$sender_id&route=4&country=91";

        return $this->makeAPICall($url, 'MSG91');
    }

    /**
     * Send via Textlocal
     */
    private function sendViaTextlocal($mobile, $otp, $message) {
        $api_key = $this->sms_settings->textlocal_api_key;
        $sender_id = $this->sms_settings->textlocal_sender_id;
        $encoded_message = urlencode($message);

        $url = "https://api.textlocal.in/send/?apikey=$api_key&numbers=$mobile&message=$encoded_message&sender=$sender_id";

        return $this->makeAPICall($url, 'Textlocal');
    }
    
    /**
     * Make API call with error handling
     */
    private function makeAPICall($url, $provider_name) {
        $result = [
            'success' => false,
            'message' => '',
            'provider' => $provider_name,
            'response' => ''
        ];
        
        // Try cURL first
        if (extension_loaded('curl')) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($error) {
                $result['message'] = "cURL Error: $error";
                return $result;
            }
            
            if ($http_code == 200) {
                $result['success'] = true;
                $result['message'] = "SMS sent successfully via $provider_name";
                $result['response'] = $response;
                return $result;
            } else {
                $result['message'] = "HTTP Error: $http_code - Response: $response";
                $result['response'] = $response;
                return $result;
            }
        }
        
        // Fallback to file() function
        if (ini_get('allow_url_fopen')) {
            $response = @file_get_contents($url);
            if ($response !== false) {
                $result['success'] = true;
                $result['message'] = "SMS sent successfully via $provider_name (file_get_contents)";
                $result['response'] = $response;
                return $result;
            } else {
                $result['message'] = "file_get_contents failed";
                return $result;
            }
        }
        
        $result['message'] = "No available method to send SMS (cURL and file_get_contents both unavailable)";
        return $result;
    }
    
    /**
     * Log errors for debugging
     */
    private function logError($message, $details) {
        if ($this->debug_mode) {
            $log_entry = date('Y-m-d H:i:s') . " - $message\n";
            $log_entry .= "Details: " . print_r($details, true) . "\n";
            $log_entry .= str_repeat("-", 50) . "\n";
            
            file_put_contents('sms_error_log.txt', $log_entry, FILE_APPEND | LOCK_EX);
        }
    }
    
    /**
     * Test SMS configuration
     */
    public function testConfiguration($test_mobile = "**********") {
        echo "Testing SMS Configuration...\n\n";
        
        // Test with dummy OTP
        $test_otp = "1234";
        $result = $this->sendOTP($test_mobile, $test_otp);
        
        echo "Provider: " . $result['provider'] . "\n";
        echo "Success: " . ($result['success'] ? 'Yes' : 'No') . "\n";
        echo "Message: " . $result['message'] . "\n";
        echo "Response: " . $result['response'] . "\n\n";
        
        // Check PHP configuration
        echo "PHP Configuration:\n";
        echo "cURL: " . (extension_loaded('curl') ? 'Available' : 'Not Available') . "\n";
        echo "allow_url_fopen: " . (ini_get('allow_url_fopen') ? 'Enabled' : 'Disabled') . "\n";
        echo "OpenSSL: " . (extension_loaded('openssl') ? 'Available' : 'Not Available') . "\n";
        
        return $result;
    }
}

// Usage example:
// $sms = new SMSHandler(true); // true for debug mode
// $result = $sms->sendOTP("**********", "1234");
// if ($result['success']) {
//     echo "OTP sent successfully!";
// } else {
//     echo "Failed to send OTP: " . $result['message'];
// }
?>
