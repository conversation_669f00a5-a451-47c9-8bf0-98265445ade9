<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>PhonePe Production Deployment - Go Live Checklist</title>
    
    <!-- CSS Files -->
    <link href="css/bootstrap.css" rel="stylesheet">
    <link href="css/custom.css" rel="stylesheet">
    <link href="css/icon-fixes.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/48403ccd1a.js" crossorigin="anonymous"></script>
    
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: green; background: #e8f5e8; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid green; }
        .error { color: red; background: #ffe8e8; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid red; }
        .warning { color: orange; background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid orange; }
        .info { color: blue; background: #e8f0ff; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid blue; }
        .critical { color: #721c24; background: #f8d7da; padding: 20px; margin: 15px 0; border-radius: 8px; border: 2px solid #dc3545; }
        .btn { display: inline-block; padding: 12px 25px; background: #007cba; color: white; text-decoration: none; border: none; border-radius: 6px; margin: 5px; cursor: pointer; font-weight: 600; }
        .btn:hover { background: #005a87; text-decoration: none; color: white; transform: translateY(-1px); }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; }
        .btn-phonepe { background: linear-gradient(135deg, #5f27cd 0%, #7c4dff 100%); }
        .checklist-section { background: #f8f9fa; padding: 25px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #007cba; }
        .checklist-item { display: flex; align-items: center; margin: 12px 0; padding: 10px; background: white; border-radius: 6px; }
        .checklist-item input[type="checkbox"] { margin-right: 15px; transform: scale(1.3); }
        .checklist-item.completed { background: #e8f5e8; border-left: 3px solid #28a745; }
        .checklist-item.critical { background: #fff3e0; border-left: 3px solid #ff9800; }
        .status-badge { padding: 6px 12px; border-radius: 15px; font-size: 12px; font-weight: 600; margin-left: 10px; }
        .status-ready { background: #d4edda; color: #155724; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-critical { background: #f8d7da; color: #721c24; }
        .deployment-step { background: white; padding: 20px; margin: 15px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .deployment-step h4 { color: #5f27cd; margin-bottom: 15px; }
        .code-block { background: #f8f9fa; padding: 15px; border-radius: 6px; font-family: monospace; font-size: 13px; margin: 10px 0; overflow-x: auto; }
        .progress-bar { width: 100%; height: 25px; background: #e9ecef; border-radius: 12px; overflow: hidden; margin: 15px 0; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #28a745, #20c997); transition: width 0.3s ease; }
        .environment-comparison { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
        .env-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .env-sandbox { border-left: 4px solid #ffc107; }
        .env-production { border-left: 4px solid #28a745; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 PhonePe Production Deployment</h1>
        <p class="text-muted">Complete go-live checklist and production deployment guide</p>
        
        <!-- Current Status -->
        <div class="info">
            <h3><i class="fa fa-info-circle"></i> Current Deployment Status</h3>
            <div class="row">
                <div class="col-md-4">
                    <strong>Environment:</strong> 
                    <span class="status-badge status-pending">Sandbox (Testing)</span>
                </div>
                <div class="col-md-4">
                    <strong>Integration:</strong> 
                    <span class="status-badge status-ready">Complete</span>
                </div>
                <div class="col-md-4">
                    <strong>Production Ready:</strong> 
                    <span class="status-badge status-pending">Pending Checklist</span>
                </div>
            </div>
            
            <div class="progress-bar">
                <div class="progress-fill" style="width: 75%;" id="deployment-progress"></div>
            </div>
            <p class="text-center"><strong>Deployment Progress: 75%</strong> - Ready for production preparation</p>
        </div>

        <!-- Critical Requirements -->
        <div class="critical">
            <h3><i class="fa fa-exclamation-triangle"></i> Critical Requirements for Production</h3>
            <ul>
                <li><strong>SSL Certificate:</strong> Valid HTTPS certificate is mandatory</li>
                <li><strong>PhonePe Approval:</strong> Production merchant account must be approved</li>
                <li><strong>Domain Verification:</strong> Domain must be verified with PhonePe</li>
                <li><strong>KYC Completion:</strong> Complete business KYC verification</li>
                <li><strong>Testing Complete:</strong> All sandbox testing must be successful</li>
            </ul>
        </div>

        <!-- Environment Comparison -->
        <div class="checklist-section">
            <h3><i class="fa fa-server"></i> Environment Comparison</h3>
            <div class="environment-comparison">
                <div class="env-card env-sandbox">
                    <h4><i class="fa fa-flask"></i> Sandbox (Current)</h4>
                    <ul>
                        <li><strong>API URL:</strong> api-preprod.phonepe.com</li>
                        <li><strong>Purpose:</strong> Testing & Development</li>
                        <li><strong>Real Money:</strong> No</li>
                        <li><strong>Test Cards:</strong> Available</li>
                        <li><strong>Approval:</strong> Not required</li>
                        <li><strong>Webhooks:</strong> Testing only</li>
                    </ul>
                </div>
                <div class="env-card env-production">
                    <h4><i class="fa fa-globe"></i> Production (Target)</h4>
                    <ul>
                        <li><strong>API URL:</strong> api.phonepe.com</li>
                        <li><strong>Purpose:</strong> Live Transactions</li>
                        <li><strong>Real Money:</strong> Yes</li>
                        <li><strong>Test Cards:</strong> Not available</li>
                        <li><strong>Approval:</strong> Required</li>
                        <li><strong>Webhooks:</strong> Live notifications</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Pre-Production Checklist -->
        <div class="checklist-section">
            <h3><i class="fa fa-check-square"></i> Pre-Production Checklist</h3>
            
            <h4>1. PhonePe Account & Credentials</h4>
            <div class="checklist-item critical">
                <input type="checkbox" id="check1">
                <label for="check1">PhonePe merchant account approved for production</label>
                <span class="status-badge status-critical">Critical</span>
            </div>
            <div class="checklist-item critical">
                <input type="checkbox" id="check2">
                <label for="check2">Production Merchant ID obtained</label>
                <span class="status-badge status-critical">Critical</span>
            </div>
            <div class="checklist-item critical">
                <input type="checkbox" id="check3">
                <label for="check3">Production Salt Key obtained</label>
                <span class="status-badge status-critical">Critical</span>
            </div>
            <div class="checklist-item">
                <input type="checkbox" id="check4">
                <label for="check4">Business KYC verification completed</label>
                <span class="status-badge status-pending">Required</span>
            </div>
            
            <h4>2. Technical Requirements</h4>
            <div class="checklist-item critical">
                <input type="checkbox" id="check5">
                <label for="check5">Valid SSL certificate installed (HTTPS)</label>
                <span class="status-badge status-critical">Critical</span>
            </div>
            <div class="checklist-item">
                <input type="checkbox" id="check6">
                <label for="check6">Domain verified with PhonePe</label>
                <span class="status-badge status-pending">Required</span>
            </div>
            <div class="checklist-item">
                <input type="checkbox" id="check7">
                <label for="check7">Webhook URLs configured in PhonePe dashboard</label>
                <span class="status-badge status-pending">Required</span>
            </div>
            <div class="checklist-item">
                <input type="checkbox" id="check8">
                <label for="check8">Database backup completed</label>
                <span class="status-badge status-ready">Recommended</span>
            </div>
            
            <h4>3. Testing & Validation</h4>
            <div class="checklist-item">
                <input type="checkbox" id="check9">
                <label for="check9">All sandbox payment scenarios tested successfully</label>
                <span class="status-badge status-ready">Complete</span>
            </div>
            <div class="checklist-item">
                <input type="checkbox" id="check10">
                <label for="check10">Webhook notifications tested and working</label>
                <span class="status-badge status-pending">Required</span>
            </div>
            <div class="checklist-item">
                <input type="checkbox" id="check11">
                <label for="check11">Database updates verified for all scenarios</label>
                <span class="status-badge status-ready">Complete</span>
            </div>
            <div class="checklist-item">
                <input type="checkbox" id="check12">
                <label for="check12">Error handling tested (failed payments, timeouts)</label>
                <span class="status-badge status-ready">Complete</span>
            </div>
            
            <h4>4. Security & Compliance</h4>
            <div class="checklist-item">
                <input type="checkbox" id="check13">
                <label for="check13">Security audit completed</label>
                <span class="status-badge status-pending">Recommended</span>
            </div>
            <div class="checklist-item">
                <input type="checkbox" id="check14">
                <label for="check14">PCI DSS compliance verified</label>
                <span class="status-badge status-pending">Required</span>
            </div>
            <div class="checklist-item">
                <input type="checkbox" id="check15">
                <label for="check15">Data encryption verified</label>
                <span class="status-badge status-ready">Complete</span>
            </div>
        </div>

        <!-- Production Deployment Steps -->
        <div class="checklist-section">
            <h3><i class="fa fa-rocket"></i> Production Deployment Steps</h3>
            
            <div class="deployment-step">
                <h4>Step 1: Backup Current System</h4>
                <p>Create complete backup before making any production changes:</p>
                <div class="code-block">
# Database backup
mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql

# File backup
tar -czf website_backup_$(date +%Y%m%d_%H%M%S).tar.gz /path/to/website/
                </div>
                <button class="btn btn-warning" onclick="markStepComplete(1)">
                    <i class="fa fa-check"></i> Backup Completed
                </button>
            </div>
            
            <div class="deployment-step">
                <h4>Step 2: Update PhonePe Configuration</h4>
                <p>Switch to production credentials in admin panel:</p>
                <ol>
                    <li>Go to <code>premium_admin/PhonePeSettings.php</code></li>
                    <li>Enter production Merchant ID</li>
                    <li>Enter production Salt Key</li>
                    <li>Set Salt Index (usually 1)</li>
                    <li>Change Environment to <strong>Production</strong></li>
                    <li>Keep Status as <strong>Enabled</strong></li>
                </ol>
                <a href="premium_admin/PhonePeSettings.php" class="btn btn-phonepe" target="_blank">
                    <i class="fa fa-cog"></i> Configure Production Settings
                </a>
            </div>
            
            <div class="deployment-step">
                <h4>Step 3: Update Webhook URLs</h4>
                <p>Configure production webhook URLs in PhonePe merchant dashboard:</p>
                <div class="code-block">
Redirect URL: https://<?php echo $_SERVER['HTTP_HOST']; ?>/phonepe_callback.php
Webhook URL: https://<?php echo $_SERVER['HTTP_HOST']; ?>/phonepe_webhook.php
                </div>
                <div class="warning">
                    <strong>Important:</strong> Ensure these URLs are accessible over HTTPS and return proper responses.
                </div>
            </div>
            
            <div class="deployment-step">
                <h4>Step 4: Production Testing</h4>
                <p>Perform final testing with production environment:</p>
                <ol>
                    <li>Test with small amount (₹1)</li>
                    <li>Verify payment success flow</li>
                    <li>Test payment failure scenarios</li>
                    <li>Verify webhook notifications</li>
                    <li>Check database updates</li>
                    <li>Test refund process (if applicable)</li>
                </ol>
                <button class="btn btn-success" onclick="startProductionTest()">
                    <i class="fa fa-test-tube"></i> Start Production Test
                </button>
            </div>
            
            <div class="deployment-step">
                <h4>Step 5: Monitor & Verify</h4>
                <p>Monitor the system for the first few transactions:</p>
                <ul>
                    <li>Check transaction logs</li>
                    <li>Verify webhook delivery</li>
                    <li>Monitor error rates</li>
                    <li>Check customer feedback</li>
                </ul>
                <a href="logs/phonepe_transactions.log" class="btn btn-info" target="_blank">
                    <i class="fa fa-file-alt"></i> View Transaction Logs
                </a>
            </div>
        </div>

        <!-- Production Configuration Template -->
        <div class="info">
            <h3><i class="fa fa-code"></i> Production Configuration Template</h3>
            <p>Use this template for your production configuration:</p>
            <div class="code-block">
// Production PhonePe Configuration
Merchant ID: [Your Production Merchant ID]
Salt Key: [Your Production Salt Key]
Salt Index: 1
Environment: Production
Status: Enabled

// Webhook URLs (HTTPS required)
Redirect URL: https://yourdomain.com/phonepe_callback.php
Webhook URL: https://yourdomain.com/phonepe_webhook.php

// Security Settings
SSL Certificate: Valid and Active
Domain Verification: Completed
KYC Status: Approved
            </div>
        </div>

        <!-- Post-Deployment Monitoring -->
        <div class="warning">
            <h3><i class="fa fa-chart-line"></i> Post-Deployment Monitoring</h3>
            <p><strong>Monitor these metrics for the first 48 hours:</strong></p>
            <div class="row">
                <div class="col-md-6">
                    <h4>Technical Metrics:</h4>
                    <ul>
                        <li>Payment success rate</li>
                        <li>API response times</li>
                        <li>Webhook delivery rate</li>
                        <li>Error rates and types</li>
                        <li>Database performance</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h4>Business Metrics:</h4>
                    <ul>
                        <li>Transaction volume</li>
                        <li>Revenue processing</li>
                        <li>Customer complaints</li>
                        <li>Membership activations</li>
                        <li>Refund requests</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Emergency Rollback Plan -->
        <div class="error">
            <h3><i class="fa fa-undo"></i> Emergency Rollback Plan</h3>
            <p><strong>If issues occur in production:</strong></p>
            <ol>
                <li><strong>Immediate:</strong> Disable PhonePe in admin panel</li>
                <li><strong>Fallback:</strong> Enable alternative payment methods</li>
                <li><strong>Restore:</strong> Revert to sandbox configuration if needed</li>
                <li><strong>Investigate:</strong> Check logs and identify issues</li>
                <li><strong>Fix:</strong> Resolve issues before re-enabling</li>
            </ol>
            <button class="btn btn-danger" onclick="emergencyDisable()">
                <i class="fa fa-stop"></i> Emergency Disable PhonePe
            </button>
        </div>

        <!-- Support Contacts -->
        <div class="info">
            <h3><i class="fa fa-headset"></i> Support Contacts</h3>
            <div class="row">
                <div class="col-md-6">
                    <h4>PhonePe Support:</h4>
                    <ul>
                        <li><strong>Email:</strong> <EMAIL></li>
                        <li><strong>Merchant Support:</strong> <EMAIL></li>
                        <li><strong>Technical Issues:</strong> <EMAIL></li>
                        <li><strong>Documentation:</strong> developer.phonepe.com</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h4>Integration Support:</h4>
                    <ul>
                        <li><strong>Test Page:</strong> test_phonepe_integration.php</li>
                        <li><strong>Admin Panel:</strong> premium_admin/PhonePeSettings.php</li>
                        <li><strong>Transaction Logs:</strong> logs/phonepe_transactions.log</li>
                        <li><strong>Documentation:</strong> PhonePe_Integration_Guide.md</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="text-center" style="margin-top: 30px;">
            <a href="premium_admin/PhonePeSettings.php" class="btn btn-phonepe">
                <i class="fa fa-cog"></i> Configure Production
            </a>
            <a href="test_phonepe_integration.php" class="btn btn-success">
                <i class="fa fa-test-tube"></i> Final Testing
            </a>
            <a href="paymentOptions.php?pid=1" class="btn btn-warning">
                <i class="fa fa-credit-card"></i> Test Live Payment
            </a>
            <button class="btn btn-info" onclick="downloadChecklist()">
                <i class="fa fa-download"></i> Download Checklist
            </button>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/jquery.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script>
        $(document).ready(function() {
            // Update progress based on completed items
            updateProgress();
            
            // Handle checkbox changes
            $('input[type="checkbox"]').change(function() {
                updateProgress();
                if ($(this).is(':checked')) {
                    $(this).closest('.checklist-item').addClass('completed');
                } else {
                    $(this).closest('.checklist-item').removeClass('completed');
                }
            });
        });
        
        function updateProgress() {
            var total = $('input[type="checkbox"]').length;
            var completed = $('input[type="checkbox"]:checked').length;
            var percentage = Math.round((completed / total) * 100);
            
            $('#deployment-progress').css('width', percentage + '%');
            
            if (percentage >= 90) {
                $('#deployment-progress').css('background', 'linear-gradient(90deg, #28a745, #20c997)');
            } else if (percentage >= 70) {
                $('#deployment-progress').css('background', 'linear-gradient(90deg, #ffc107, #fd7e14)');
            } else {
                $('#deployment-progress').css('background', 'linear-gradient(90deg, #dc3545, #e83e8c)');
            }
        }
        
        function markStepComplete(step) {
            alert('Step ' + step + ' marked as complete. Please ensure all requirements are met before proceeding.');
        }
        
        function startProductionTest() {
            if (confirm('Are you sure you want to start production testing? This will use real money.')) {
                window.open('paymentOptions.php?pid=1', '_blank');
            }
        }
        
        function emergencyDisable() {
            if (confirm('This will immediately disable PhonePe payments. Are you sure?')) {
                // In real implementation, this would make an AJAX call to disable PhonePe
                alert('PhonePe payments have been disabled. Users will see alternative payment methods only.');
            }
        }
        
        function downloadChecklist() {
            // Create downloadable checklist
            var checklist = "PhonePe Production Deployment Checklist\n\n";
            checklist += "Pre-Production Requirements:\n";
            checklist += "☐ PhonePe merchant account approved\n";
            checklist += "☐ Production credentials obtained\n";
            checklist += "☐ SSL certificate installed\n";
            checklist += "☐ Domain verified with PhonePe\n";
            checklist += "☐ Webhook URLs configured\n";
            checklist += "☐ All testing completed\n";
            checklist += "☐ Security audit done\n";
            checklist += "☐ Backup completed\n\n";
            checklist += "Deployment Steps:\n";
            checklist += "☐ Update production configuration\n";
            checklist += "☐ Configure webhook URLs\n";
            checklist += "☐ Perform production testing\n";
            checklist += "☐ Monitor initial transactions\n";
            checklist += "☐ Verify all systems working\n";
            
            var blob = new Blob([checklist], { type: 'text/plain' });
            var url = window.URL.createObjectURL(blob);
            var a = document.createElement('a');
            a.href = url;
            a.download = 'phonepe_production_checklist.txt';
            a.click();
            window.URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
