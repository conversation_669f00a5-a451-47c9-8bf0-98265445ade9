<?php
/**
 * Comprehensive OTP Issue Diagnosis Script
 * This script will help identify and fix OTP sending issues
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>OTP Issue Diagnosis Report</h1>";
echo "<hr>";

// Include necessary files
include_once 'databaseConn.php';
include_once 'sms_handler.php';

$DatabaseCo = new DatabaseConn();

echo "<h2>1. Database Connection Test</h2>";
if ($DatabaseCo->dbLink) {
    echo "✅ Database connection: <strong>SUCCESS</strong><br>";
    
    // Test if register table exists and has mobile numbers
    $test_query = $DatabaseCo->dbLink->query("SELECT COUNT(*) as count FROM register WHERE mobile IS NOT NULL AND mobile != ''");
    if ($test_query) {
        $result = mysqli_fetch_object($test_query);
        echo "✅ Register table accessible: <strong>SUCCESS</strong><br>";
        echo "📊 Total users with mobile numbers: <strong>" . $result->count . "</strong><br>";
    } else {
        echo "❌ Register table access: <strong>FAILED</strong><br>";
        echo "Error: " . mysqli_error($DatabaseCo->dbLink) . "<br>";
    }
} else {
    echo "❌ Database connection: <strong>FAILED</strong><br>";
    echo "Error: " . mysqli_connect_error() . "<br>";
}

echo "<hr>";

echo "<h2>2. PHP Configuration Check</h2>";
echo "PHP Version: <strong>" . phpversion() . "</strong><br>";
echo "cURL Extension: <strong>" . (extension_loaded('curl') ? '✅ Available' : '❌ Not Available') . "</strong><br>";
echo "OpenSSL Extension: <strong>" . (extension_loaded('openssl') ? '✅ Available' : '❌ Not Available') . "</strong><br>";
echo "allow_url_fopen: <strong>" . (ini_get('allow_url_fopen') ? '✅ Enabled' : '❌ Disabled') . "</strong><br>";
echo "file_get_contents: <strong>" . (function_exists('file_get_contents') ? '✅ Available' : '❌ Not Available') . "</strong><br>";

echo "<hr>";

echo "<h2>3. Fast2SMS API Test</h2>";

// Test SMS handler
$sms = new SMSHandler(true);
$test_mobile = "**********"; // Test mobile number
$test_otp = "1234";

echo "<h3>Testing with Enhanced SMS Handler:</h3>";
$result = $sms->sendOTP($test_mobile, $test_otp);

echo "Provider: <strong>" . $result['provider'] . "</strong><br>";
echo "Success: <strong>" . ($result['success'] ? '✅ YES' : '❌ NO') . "</strong><br>";
echo "Message: <strong>" . htmlspecialchars($result['message']) . "</strong><br>";
echo "Response: <strong>" . htmlspecialchars($result['response']) . "</strong><br>";

echo "<hr>";

echo "<h2>4. Original API Test (mobile-apis.php)</h2>";

// Test original method
$order_id = $test_otp;
$mno = $test_mobile;
include 'mobile-apis.php';

echo "API URL: <strong>" . htmlspecialchars($url) . "</strong><br>";

// Test with file() function
echo "<h3>Testing with file() function:</h3>";
$ret = @file($url);
if ($ret === false) {
    echo "Status: <strong>❌ FAILED</strong><br>";
    echo "Error: Unable to fetch URL using file() function<br>";
} else {
    echo "Status: <strong>✅ SUCCESS</strong><br>";
    echo "Response: <strong>" . htmlspecialchars(implode('', $ret)) . "</strong><br>";
}

echo "<hr>";

echo "<h2>5. Network Connectivity Test</h2>";

// Test basic connectivity to Fast2SMS
$test_url = "https://www.fast2sms.com";
echo "Testing connectivity to Fast2SMS...<br>";

if (extension_loaded('curl')) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $test_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $result = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "❌ Network connectivity: <strong>FAILED</strong><br>";
        echo "cURL Error: " . $error . "<br>";
    } else {
        echo "✅ Network connectivity: <strong>SUCCESS</strong><br>";
        echo "HTTP Code: " . $http_code . "<br>";
    }
} else {
    echo "⚠️ cURL not available for network test<br>";
}

echo "<hr>";

echo "<h2>6. Recommendations</h2>";

if (!$result['success']) {
    echo "<div style='background-color: #ffebee; padding: 15px; border-left: 4px solid #f44336;'>";
    echo "<h3>🚨 Issues Found:</h3>";
    echo "<ol>";
    echo "<li><strong>Fast2SMS API Issue:</strong> The API requires website verification before OTP messages can be sent.</li>";
    echo "<li><strong>Action Required:</strong> Login to your Fast2SMS dashboard and complete website verification.</li>";
    echo "<li><strong>Alternative:</strong> Consider using DLT route or switch to a different SMS provider.</li>";
    echo "</ol>";
    echo "</div>";
}

echo "<div style='background-color: #e8f5e8; padding: 15px; border-left: 4px solid #4caf50; margin-top: 20px;'>";
echo "<h3>💡 Solutions:</h3>";
echo "<ol>";
echo "<li><strong>Complete Fast2SMS Verification:</strong>";
echo "<ul>";
echo "<li>Login to your Fast2SMS dashboard</li>";
echo "<li>Go to OTP Message menu</li>";
echo "<li>Complete website verification process</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>Update API Configuration:</strong>";
echo "<ul>";
echo "<li>Get a fresh API key from Fast2SMS</li>";
echo "<li>Update the authorization key in mobile-apis.php</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>Use Alternative SMS Provider:</strong>";
echo "<ul>";
echo "<li>Consider MSG91, Textlocal, or other providers</li>";
echo "<li>Update the SMS handler accordingly</li>";
echo "</ul>";
echo "</li>";
echo "</ol>";
echo "</div>";

echo "<hr>";

echo "<h2>7. Error Log Check</h2>";
if (file_exists('sms_error_log.txt')) {
    echo "📄 SMS Error Log found. Recent entries:<br>";
    echo "<pre style='background-color: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
    $log_content = file_get_contents('sms_error_log.txt');
    echo htmlspecialchars(substr($log_content, -1000)); // Show last 1000 characters
    echo "</pre>";
} else {
    echo "ℹ️ No SMS error log found yet.<br>";
}

echo "<hr>";
echo "<p><em>Diagnosis completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
