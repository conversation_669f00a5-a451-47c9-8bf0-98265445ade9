-- SQL script to create SMS settings and theme settings tables

-- Create SMS Settings Table
CREATE TABLE IF NOT EXISTS `sms_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `provider` varchar(50) NOT NULL DEFAULT 'fast2sms',
  `fast2sms_api_key` varchar(255) DEFAULT NULL,
  `fast2sms_sender_id` varchar(10) DEFAULT NULL,
  `fast2sms_route` varchar(20) DEFAULT 'otp',
  `msg91_api_key` varchar(255) DEFAULT NULL,
  `msg91_sender_id` varchar(10) DEFAULT NULL,
  `textlocal_api_key` varchar(255) DEFAULT NULL,
  `textlocal_sender_id` varchar(10) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `test_mobile` varchar(15) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  <PERSON>IMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default SMS settings
INSERT INTO `sms_settings` (`id`, `provider`, `fast2sms_api_key`, `fast2sms_sender_id`, `fast2sms_route`, `is_active`) 
VALUES (1, 'fast2sms', 'r2oeVHLQzpadwlNAGiEXtBfguK1IWS7YxZ58ROqn3DhMFvy9UbjN4C6iqf59nFPW8yD0wuZslmAJRtOr', 'FSTSMS', 'otp', 1)
ON DUPLICATE KEY UPDATE 
`fast2sms_api_key` = 'r2oeVHLQzpadwlNAGiEXtBfguK1IWS7YxZ58ROqn3DhMFvy9UbjN4C6iqf59nFPW8yD0wuZslmAJRtOr';

-- Create Theme Settings Table
CREATE TABLE IF NOT EXISTS `theme_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `primary_color` varchar(7) DEFAULT '#499202',
  `secondary_color` varchar(7) DEFAULT '#e47203',
  `accent_color` varchar(7) DEFAULT '#089cbe',
  `success_color` varchar(7) DEFAULT '#00a65a',
  `warning_color` varchar(7) DEFAULT '#f39c12',
  `danger_color` varchar(7) DEFAULT '#f56954',
  `dark_color` varchar(7) DEFAULT '#222222',
  `light_color` varchar(7) DEFAULT '#f4f4f4',
  `button_primary_color` varchar(7) DEFAULT '#499202',
  `button_secondary_color` varchar(7) DEFAULT '#e47203',
  `link_color` varchar(7) DEFAULT '#089cbe',
  `link_hover_color` varchar(7) DEFAULT '#067a9b',
  `header_bg_color` varchar(7) DEFAULT '#ffffff',
  `footer_bg_color` varchar(7) DEFAULT '#2c3e50',
  `sidebar_bg_color` varchar(7) DEFAULT '#f8f9fa',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default theme settings
INSERT INTO `theme_settings` (`id`, `is_active`) 
VALUES (1, 1)
ON DUPLICATE KEY UPDATE `is_active` = 1;

-- Add SMS and theme settings columns to site_config table if they don't exist
ALTER TABLE `site_config` 
ADD COLUMN IF NOT EXISTS `sms_provider` varchar(50) DEFAULT 'fast2sms',
ADD COLUMN IF NOT EXISTS `sms_api_key` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `theme_primary_color` varchar(7) DEFAULT '#499202',
ADD COLUMN IF NOT EXISTS `theme_secondary_color` varchar(7) DEFAULT '#e47203';
