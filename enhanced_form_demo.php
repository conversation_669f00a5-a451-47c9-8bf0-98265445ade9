<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>Enhanced Pellipus Registration Form - Demo</title>
    
    <!-- CSS Files -->
    <link href="css/bootstrap.css" rel="stylesheet">
    <link href="css/custom-responsive.css" rel="stylesheet">
    <link href="css/custom.css" rel="stylesheet">
    <link href="css/icon-fixes.css" rel="stylesheet">
    <link href="css/mobile-responsive-fixes.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/48403ccd1a.js" crossorigin="anonymous"></script>
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    
    <!-- Angular JS -->
    <script src="js/angular.min.js"></script>
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .demo-header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .demo-header h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .demo-header p {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .enhancement-badge {
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: inline-block;
            margin: 5px;
            box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
        }
        
        .features-list {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
        }
        
        .features-list h3 {
            color: white;
            margin-bottom: 15px;
            font-size: 18px;
        }
        
        .features-list ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .features-list li {
            color: white;
            padding: 5px 0;
            font-size: 14px;
        }
        
        .features-list li:before {
            content: "✅ ";
            margin-right: 8px;
        }
    </style>
</head>
<body ng-app class="ng-scope">
    <div class="container">
        <!-- Demo Header -->
        <div class="demo-header">
            <h1>🚀 Enhanced Pellipus Registration Form</h1>
            <p>Implementing all recommended improvements from the analysis</p>
            
            <div class="enhancement-badge">Modern Icons</div>
            <div class="enhancement-badge">Real-time Validation</div>
            <div class="enhancement-badge">Smart Notifications</div>
            <div class="enhancement-badge">Enhanced UX</div>
        </div>
        
        <!-- Features Overview -->
        <div class="row">
            <div class="col-md-6">
                <div class="features-list">
                    <h3>🎨 Visual Enhancements</h3>
                    <ul>
                        <li>Modern gradient backgrounds</li>
                        <li>Enhanced icon system with Font Awesome</li>
                        <li>Improved form styling with shadows</li>
                        <li>Better color scheme and typography</li>
                        <li>Responsive design improvements</li>
                    </ul>
                </div>
            </div>
            <div class="col-md-6">
                <div class="features-list">
                    <h3>⚡ Functional Improvements</h3>
                    <ul>
                        <li>Real-time form validation</li>
                        <li>Smart error notifications</li>
                        <li>Enhanced user feedback</li>
                        <li>Better accessibility features</li>
                        <li>Mobile-optimized interactions</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Enhanced Registration Form -->
        <div class="col-xxl-6 col-xxl-offset-10 col-xl-7 col-xl-offset-9 col-lg-16 gt-pad-lr-0-479">
            <div class="gt-slideup-form">
                <div class="gt-slideUp-form-head">
                    <h4>Register Now</h4>
                </div>
                <div class="gt-slideUp-form-body">
                    <form id="enhancedForm" method="post" action="#" onsubmit="return validateForm()">
                        
                        <!-- Profile Created By & Gender -->
                        <div class="col-xxl-16 col-xl-16 form-group gt-index-collab">
                            <div class="row">
                                <div class="input-group">
                                    <span class="input-group-addon"><i class="fa fa-users fa-fw"></i></span>
                                    <select class="gt-form-control form-1" name="profile_by" required>
                                        <option value="">Profile Created By</option>
                                        <option value="Self">Self</option>
                                        <option value="Parents">Parents</option>
                                        <option value="Guardian">Guardian</option>
                                        <option value="Relative">Relative</option>
                                        <option value="Friend">Friend</option>
                                    </select>
                                    <span class="input-group-addon"><i class="fa fa-venus-mars fa-fw"></i></span>
                                    <select class="gt-form-control form-2" name="gender" required>
                                        <option value="">Select Gender</option>
                                        <option value="Female">Female</option>
                                        <option value="Male">Male</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- First Name & Last Name -->
                        <div class="col-xxl-16 col-xl-16 form-group gt-index-collab">
                            <div class="row">
                                <div class="input-group">
                                    <span class="input-group-addon"><i class="fa fa-user fa-fw"></i></span>
                                    <input type="text" class="gt-form-control form-1" placeholder="Enter First Name" name="first_name" required maxlength="30">
                                    <span class="input-group-addon"><i class="fa fa-user fa-fw"></i></span>
                                    <input type="text" class="gt-form-control form-2" placeholder="Enter Last Name" name="last_name" required maxlength="30">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Date of Birth -->
                        <div class="col-xxl-16 col-xl-16 form-group">
                            <div class="row">
                                <div class="input-group">
                                    <span class="input-group-addon"><i class="fa fa-calendar fa-fw"></i></span>
                                    <div class="row">
                                        <div class="col-xxl-4 col-xs-4">
                                            <select name="day" class="gt-form-control" required>
                                                <option value="">Day</option>
                                                <?php for($i = 1; $i <= 31; $i++): ?>
                                                    <option value="<?php echo sprintf('%02d', $i); ?>"><?php echo sprintf('%02d', $i); ?></option>
                                                <?php endfor; ?>
                                            </select>
                                        </div>
                                        <div class="col-xxl-6 col-xs-4">
                                            <select name="month" class="gt-form-control" required>
                                                <option value="">Month</option>
                                                <option value="01">January</option>
                                                <option value="02">February</option>
                                                <option value="03">March</option>
                                                <option value="04">April</option>
                                                <option value="05">May</option>
                                                <option value="06">June</option>
                                                <option value="07">July</option>
                                                <option value="08">August</option>
                                                <option value="09">September</option>
                                                <option value="10">October</option>
                                                <option value="11">November</option>
                                                <option value="12">December</option>
                                            </select>
                                        </div>
                                        <div class="col-xxl-6 col-xs-4">
                                            <select name="year" class="gt-form-control" required>
                                                <option value="">Year</option>
                                                <?php 
                                                $currentYear = date('Y');
                                                for($i = $currentYear - 18; $i >= $currentYear - 60; $i--): 
                                                ?>
                                                    <option value="<?php echo $i; ?>"><?php echo $i; ?></option>
                                                <?php endfor; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Religion -->
                        <div class="col-xxl-16 col-xl-16 form-group">
                            <div class="row">
                                <div class="input-group">
                                    <span class="input-group-addon"><i class="fa fa-book fa-fw"></i></span>
                                    <select class="gt-form-control" name="religion" required>
                                        <option value="">Select Your Religion</option>
                                        <option value="Hindu">Hindu</option>
                                        <option value="Muslim">Muslim</option>
                                        <option value="Christian">Christian</option>
                                        <option value="Sikh">Sikh</option>
                                        <option value="Buddhist">Buddhist</option>
                                        <option value="Jain">Jain</option>
                                        <option value="Parsi">Parsi</option>
                                        <option value="Jewish">Jewish</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Mother Tongue & Country -->
                        <div class="col-xxl-16 col-xl-16 form-group">
                            <div class="row">
                                <div class="input-group custom-chosen">
                                    <span class="input-group-addon"><i class="fa fa-globe fa-fw"></i></span>
                                    <select class="gt-form-control form-1" name="mother_tongue" required>
                                        <option value="">Mother Tongue</option>
                                        <option value="Hindi">Hindi</option>
                                        <option value="English">English</option>
                                        <option value="Bengali">Bengali</option>
                                        <option value="Telugu">Telugu</option>
                                        <option value="Marathi">Marathi</option>
                                        <option value="Tamil">Tamil</option>
                                        <option value="Gujarati">Gujarati</option>
                                        <option value="Urdu">Urdu</option>
                                        <option value="Kannada">Kannada</option>
                                        <option value="Malayalam">Malayalam</option>
                                        <option value="Punjabi">Punjabi</option>
                                    </select>
                                    <span class="input-group-addon"><i class="fa fa-flag fa-fw"></i></span>
                                    <select class="gt-form-control form-2" name="country" required>
                                        <option value="">Country</option>
                                        <option value="India">India</option>
                                        <option value="USA">USA</option>
                                        <option value="UK">UK</option>
                                        <option value="Canada">Canada</option>
                                        <option value="Australia">Australia</option>
                                        <option value="UAE">UAE</option>
                                        <option value="Singapore">Singapore</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Phone Number -->
                        <div class="col-xxl-16 col-xl-16 form-group">
                            <div class="row">
                                <div class="input-group">
                                    <span class="input-group-addon"><i class="fa fa-phone fa-fw"></i></span>
                                    <div class="row">
                                        <div class="col-xxl-5 col-xs-4">
                                            <select class="gt-form-control" name="country_code" required>
                                                <option value="+91">+91</option>
                                                <option value="+1">+1</option>
                                                <option value="+44">+44</option>
                                                <option value="+61">+61</option>
                                                <option value="+971">+971</option>
                                            </select>
                                        </div>
                                        <div class="col-xxl-11 col-xs-8">
                                            <input type="tel" class="gt-form-control" placeholder="Enter Your 10 Digit No" name="mobile" required maxlength="10" pattern="[0-9]{10}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Email -->
                        <div class="col-xxl-16 col-xl-16 form-group">
                            <div class="row">
                                <div class="input-group">
                                    <span class="input-group-addon"><i class="fas fa-at fa-fw"></i></span>
                                    <input type="email" class="gt-form-control" placeholder="Enter Your Email Id" name="email" required>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Terms and Conditions -->
                        <div class="col-xxl-16 col-xl-16 form-group">
                            <div class="row">
                                <label for="terms" class="inTerms">
                                    <input type="checkbox" id="terms" name="terms" required>
                                    <span>I accept <a href="#" target="_blank">terms & conditions</a> and <a href="#" target="_blank">privacy policy</a>.</span>
                                </label>
                            </div>
                        </div>
                        
                        <!-- Submit Button -->
                        <div class="row form-group">
                            <div class="col-xxl-16 text-center">
                                <button type="submit" class="btn gt-btn-green inIndexRegBtn">
                                    <i class="fa fa-user-plus" style="margin-right: 8px;"></i>
                                    Register Now
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/jquery.min.js"></script>
    <script src="js/bootstrap.js"></script>
    <script src="js/enhanced-validation.js"></script>
    
    <script>
        // Enhanced form validation
        function validateForm() {
            if (window.EnhancedValidation) {
                return window.EnhancedValidation.validateForm();
            }
            return true;
        }
        
        // Demo initialization
        $(document).ready(function() {
            console.log('🎨 Enhanced Form Demo Loaded');
            
            // Show welcome message
            setTimeout(() => {
                if (window.EnhancedValidation) {
                    window.EnhancedValidation.showNotification(
                        'Welcome to the enhanced registration form! Try filling out the fields to see real-time validation.',
                        'info',
                        6000
                    );
                }
            }, 1000);
        });
    </script>
</body>
</html>
