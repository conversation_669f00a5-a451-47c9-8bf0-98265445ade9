<?php
/**
 * PhonePe Payment Gateway Settings - Admin Panel
 */

include_once '../databaseConn.php';
include_once '../phonepe_integration.php';
include_once '../lib/requestHandler.php';

$DatabaseCo = new DatabaseConn();

// Check admin session
if (!isset($_SESSION['admin_user_name'])) {
    echo "<script>window.location='index.php';</script>";
    exit;
}

$message = '';
$message_type = '';

// Handle form submission
if (isset($_POST['update_phonepe_settings'])) {
    try {
        $config_data = [
            'merchant_id' => mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['merchant_id']),
            'salt_key' => mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['salt_key']),
            'salt_index' => mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['salt_index']),
            'is_production' => mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['is_production']),
            'status' => mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['status'])
        ];
        
        if (PhonePeConfig::updateConfig($DatabaseCo->dbLink, $config_data)) {
            $message = 'PhonePe settings updated successfully!';
            $message_type = 'success';
        } else {
            $message = 'Failed to update PhonePe settings.';
            $message_type = 'error';
        }
    } catch (Exception $e) {
        $message = 'Error: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// Handle test payment
if (isset($_POST['test_phonepe'])) {
    try {
        $config = PhonePeConfig::getConfig($DatabaseCo->dbLink);
        
        if ($config && $config->status == 'APPROVED') {
            $phonepe = new PhonePePayment(
                $config->phonepe_merchant_id,
                $config->phonepe_salt_key,
                $config->phonepe_salt_index,
                $config->phonepe_is_production == 'YES'
            );
            
            // Test with dummy data
            $test_transaction_id = PhonePeUtils::generateTransactionId('TEST');
            $test_user_data = [
                'user_id' => 'TEST_USER',
                'mobile' => '9999999999',
                'email' => '<EMAIL>',
                'name' => 'Test User'
            ];
            
            $test_response = $phonepe->createPayment($test_transaction_id, 1, $test_user_data);
            
            if ($test_response['success']) {
                $message = 'PhonePe test successful! Payment gateway is working correctly.';
                $message_type = 'success';
            } else {
                $message = 'PhonePe test failed: ' . json_encode($test_response);
                $message_type = 'error';
            }
        } else {
            $message = 'PhonePe is not configured or not approved.';
            $message_type = 'error';
        }
    } catch (Exception $e) {
        $message = 'Test error: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// Get current configuration
$current_config = PhonePeConfig::getConfig($DatabaseCo->dbLink);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>PhonePe Settings - Admin Panel</title>
    
    <!-- CSS Files -->
    <link href="../css/bootstrap.css" rel="stylesheet">
    <link href="../css/custom.css" rel="stylesheet">
    <link href="../css/icon-fixes.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/48403ccd1a.js" crossorigin="anonymous"></script>
    
    <style>
        body { background: #f8f9fa; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .admin-container { max-width: 1000px; margin: 30px auto; padding: 0 20px; }
        .admin-header { background: linear-gradient(135deg, #5f27cd 0%, #7c4dff 100%); color: white; padding: 20px; border-radius: 10px 10px 0 0; }
        .admin-content { background: white; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 20px; }
        .form-control { padding: 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px; }
        .form-control:focus { border-color: #5f27cd; box-shadow: 0 0 0 3px rgba(95, 39, 205, 0.1); }
        .btn-phonepe { background: linear-gradient(135deg, #5f27cd 0%, #7c4dff 100%); color: white; border: none; padding: 12px 25px; border-radius: 6px; font-weight: 600; }
        .btn-phonepe:hover { transform: translateY(-1px); box-shadow: 0 4px 12px rgba(95, 39, 205, 0.3); color: white; }
        .alert { padding: 15px; border-radius: 6px; margin-bottom: 20px; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .config-section { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .status-badge { padding: 5px 12px; border-radius: 15px; font-size: 12px; font-weight: 600; }
        .status-approved { background: #d4edda; color: #155724; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-disabled { background: #f8d7da; color: #721c24; }
        .info-box { background: #e3f2fd; padding: 15px; border-radius: 6px; border-left: 4px solid #2196f3; margin: 15px 0; }
        .warning-box { background: #fff3e0; padding: 15px; border-radius: 6px; border-left: 4px solid #ff9800; margin: 15px 0; }
        .code-block { background: #f8f9fa; padding: 15px; border-radius: 6px; font-family: monospace; font-size: 13px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- Header -->
        <div class="admin-header">
            <h1><i class="fab fa-paypal"></i> PhonePe Payment Gateway Settings</h1>
            <p>Configure PhonePe payment gateway for your matrimony website</p>
        </div>
        
        <div class="admin-content">
            <!-- Messages -->
            <?php if ($message): ?>
            <div class="alert alert-<?php echo $message_type; ?>">
                <i class="fa fa-<?php echo $message_type == 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
                <?php echo htmlspecialchars($message); ?>
            </div>
            <?php endif; ?>
            
            <!-- Current Status -->
            <div class="config-section">
                <h3><i class="fa fa-info-circle"></i> Current Status</h3>
                <div class="row">
                    <div class="col-md-6">
                        <strong>PhonePe Status:</strong>
                        <?php if ($current_config): ?>
                            <span class="status-badge status-<?php echo strtolower($current_config->status); ?>">
                                <?php echo $current_config->status; ?>
                            </span>
                        <?php else: ?>
                            <span class="status-badge status-disabled">Not Configured</span>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-6">
                        <strong>Environment:</strong>
                        <?php if ($current_config): ?>
                            <span class="status-badge <?php echo $current_config->phonepe_is_production == 'YES' ? 'status-approved' : 'status-pending'; ?>">
                                <?php echo $current_config->phonepe_is_production == 'YES' ? 'Production' : 'Sandbox'; ?>
                            </span>
                        <?php else: ?>
                            <span class="status-badge status-disabled">Not Set</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Setup Instructions -->
            <div class="info-box">
                <h4><i class="fa fa-lightbulb"></i> PhonePe Setup Instructions</h4>
                <ol>
                    <li>Visit <a href="https://developer.phonepe.com" target="_blank">PhonePe Developer Portal</a></li>
                    <li>Create a merchant account and get your credentials</li>
                    <li>Get your Merchant ID, Salt Key, and Salt Index</li>
                    <li>Configure the settings below</li>
                    <li>Test the integration before going live</li>
                </ol>
            </div>
            
            <!-- Configuration Form -->
            <form method="POST" id="phonepe-config-form">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="merchant_id"><i class="fa fa-store"></i> Merchant ID *</label>
                            <input type="text" class="form-control" id="merchant_id" name="merchant_id" 
                                   value="<?php echo htmlspecialchars($current_config->phonepe_merchant_id ?? ''); ?>" 
                                   placeholder="Enter your PhonePe Merchant ID" required>
                            <small class="text-muted">Your unique merchant identifier from PhonePe</small>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="salt_index"><i class="fa fa-key"></i> Salt Index *</label>
                            <input type="number" class="form-control" id="salt_index" name="salt_index" 
                                   value="<?php echo htmlspecialchars($current_config->phonepe_salt_index ?? '1'); ?>" 
                                   placeholder="1" min="1" required>
                            <small class="text-muted">Usually 1 for most merchants</small>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="salt_key"><i class="fa fa-lock"></i> Salt Key *</label>
                    <input type="password" class="form-control" id="salt_key" name="salt_key" 
                           value="<?php echo htmlspecialchars($current_config->phonepe_salt_key ?? ''); ?>" 
                           placeholder="Enter your PhonePe Salt Key" required>
                    <small class="text-muted">Keep this secret and secure</small>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="is_production"><i class="fa fa-server"></i> Environment</label>
                            <select class="form-control" id="is_production" name="is_production">
                                <option value="NO" <?php echo ($current_config->phonepe_is_production ?? '') == 'NO' ? 'selected' : ''; ?>>
                                    Sandbox (Testing)
                                </option>
                                <option value="YES" <?php echo ($current_config->phonepe_is_production ?? '') == 'YES' ? 'selected' : ''; ?>>
                                    Production (Live)
                                </option>
                            </select>
                            <small class="text-muted">Use Sandbox for testing, Production for live payments</small>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="status"><i class="fa fa-toggle-on"></i> Status</label>
                            <select class="form-control" id="status" name="status">
                                <option value="APPROVED" <?php echo ($current_config->status ?? '') == 'APPROVED' ? 'selected' : ''; ?>>
                                    Enabled
                                </option>
                                <option value="PENDING" <?php echo ($current_config->status ?? '') == 'PENDING' ? 'selected' : ''; ?>>
                                    Disabled
                                </option>
                            </select>
                            <small class="text-muted">Enable/disable PhonePe payments</small>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <button type="submit" name="update_phonepe_settings" class="btn btn-phonepe">
                        <i class="fa fa-save"></i> Save PhonePe Settings
                    </button>
                    
                    <?php if ($current_config && $current_config->status == 'APPROVED'): ?>
                    <button type="submit" name="test_phonepe" class="btn btn-success" style="margin-left: 10px;">
                        <i class="fa fa-test-tube"></i> Test Integration
                    </button>
                    <?php endif; ?>
                </div>
            </form>
            
            <!-- Webhook Configuration -->
            <div class="config-section">
                <h3><i class="fa fa-webhook"></i> Webhook Configuration</h3>
                <p>Configure these URLs in your PhonePe merchant dashboard:</p>
                
                <div class="form-group">
                    <label>Redirect URL (Success/Failure):</label>
                    <div class="code-block">
                        <?php echo 'https://' . $_SERVER['HTTP_HOST'] . '/phonepe_callback.php'; ?>
                    </div>
                </div>
                
                <div class="form-group">
                    <label>Webhook URL (Server-to-Server):</label>
                    <div class="code-block">
                        <?php echo 'https://' . $_SERVER['HTTP_HOST'] . '/phonepe_webhook.php'; ?>
                    </div>
                </div>
            </div>
            
            <!-- Integration Guide -->
            <div class="warning-box">
                <h4><i class="fa fa-exclamation-triangle"></i> Important Notes</h4>
                <ul>
                    <li><strong>Security:</strong> Never share your Salt Key publicly</li>
                    <li><strong>Testing:</strong> Always test in Sandbox before going live</li>
                    <li><strong>Webhooks:</strong> Configure webhook URLs in PhonePe dashboard</li>
                    <li><strong>SSL:</strong> Ensure your website has valid SSL certificate</li>
                    <li><strong>Compliance:</strong> Follow PhonePe's integration guidelines</li>
                </ul>
            </div>
            
            <!-- Documentation Links -->
            <div class="info-box">
                <h4><i class="fa fa-book"></i> Documentation & Support</h4>
                <ul>
                    <li><a href="https://developer.phonepe.com/v1/docs/introduction-pg" target="_blank">PhonePe API Documentation</a></li>
                    <li><a href="https://developer.phonepe.com/v1/docs/payment-gateway-integration" target="_blank">Integration Guide</a></li>
                    <li><a href="https://developer.phonepe.com/v1/docs/webhooks" target="_blank">Webhook Documentation</a></li>
                    <li><a href="mailto:<EMAIL>">PhonePe Support</a></li>
                </ul>
            </div>
            
            <!-- Back to Admin -->
            <div class="text-center" style="margin-top: 30px;">
                <a href="dashboard.php" class="btn btn-secondary">
                    <i class="fa fa-arrow-left"></i> Back to Admin Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="../js/jquery.min.js"></script>
    <script src="../js/bootstrap.min.js"></script>
    <script>
        $(document).ready(function() {
            // Form validation
            $('#phonepe-config-form').on('submit', function(e) {
                var merchantId = $('#merchant_id').val().trim();
                var saltKey = $('#salt_key').val().trim();
                
                if (!merchantId || !saltKey) {
                    e.preventDefault();
                    alert('Please fill in all required fields (Merchant ID and Salt Key)');
                    return false;
                }
                
                if (saltKey.length < 10) {
                    e.preventDefault();
                    alert('Salt Key seems too short. Please verify your credentials.');
                    return false;
                }
            });
            
            // Toggle password visibility
            $('<button type="button" class="btn btn-sm btn-outline-secondary" style="margin-left: 10px;">Show</button>')
                .insertAfter('#salt_key')
                .on('click', function() {
                    var input = $('#salt_key');
                    var type = input.attr('type') === 'password' ? 'text' : 'password';
                    input.attr('type', type);
                    $(this).text(type === 'password' ? 'Show' : 'Hide');
                });
        });
    </script>
</body>
</html>
