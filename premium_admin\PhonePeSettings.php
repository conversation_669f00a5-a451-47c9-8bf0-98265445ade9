<?php
/**
 * PhonePe Payment Gateway Settings - Admin Panel
 */

include_once '../databaseConn.php';
include_once '../phonepe_integration.php';
include_once '../lib/requestHandler.php';

$DatabaseCo = new DatabaseConn();

// Check admin session
if (!isset($_SESSION['admin_user_name'])) {
    echo "<script>window.location='index.php';</script>";
    exit;
}

$message = '';
$message_type = '';

// Handle form submission
if (isset($_POST['update_phonepe_settings'])) {
    try {
        $config_data = [
            'merchant_id' => mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['merchant_id']),
            'salt_key' => mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['salt_key']),
            'salt_index' => mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['salt_index']),
            'is_production' => mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['is_production']),
            'status' => mysqli_real_escape_string($DatabaseCo->dbLink, $_POST['status'])
        ];
        
        if (PhonePeConfig::updateConfig($DatabaseCo->dbLink, $config_data)) {
            $message = 'PhonePe settings updated successfully!';
            $message_type = 'success';
        } else {
            $message = 'Failed to update PhonePe settings.';
            $message_type = 'error';
        }
    } catch (Exception $e) {
        $message = 'Error: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// Handle test payment
if (isset($_POST['test_phonepe'])) {
    try {
        $config = PhonePeConfig::getConfig($DatabaseCo->dbLink);
        
        if ($config && $config->status == 'APPROVED') {
            $phonepe = new PhonePePayment(
                $config->phonepe_merchant_id,
                $config->phonepe_salt_key,
                $config->phonepe_salt_index,
                $config->phonepe_is_production == 'YES'
            );
            
            // Test with dummy data
            $test_transaction_id = PhonePeUtils::generateTransactionId('TEST');
            $test_user_data = [
                'user_id' => 'TEST_USER',
                'mobile' => '9999999999',
                'email' => '<EMAIL>',
                'name' => 'Test User'
            ];
            
            $test_response = $phonepe->createPayment($test_transaction_id, 1, $test_user_data);
            
            if ($test_response['success']) {
                $message = 'PhonePe test successful! Payment gateway is working correctly.';
                $message_type = 'success';
            } else {
                $message = 'PhonePe test failed: ' . json_encode($test_response);
                $message_type = 'error';
            }
        } else {
            $message = 'PhonePe is not configured or not approved.';
            $message_type = 'error';
        }
    } catch (Exception $e) {
        $message = 'Test error: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// Get current configuration
$current_config = PhonePeConfig::getConfig($DatabaseCo->dbLink);
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Admin | PhonePe Settings</title>
    <meta content='width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no' name='viewport'>

    <!-- Bootstrap & custom css -->
    <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="css/custom.css" rel="stylesheet" type="text/css" />

    <!-- Font Awsome -->
    <script src="https://kit.fontawesome.com/48403ccd1a.js" crossorigin="anonymous"></script>

    <!-- Ionicons -->
    <link href="http://code.ionicframework.com/ionicons/2.0.0/css/ionicons.min.css" rel="stylesheet" type="text/css" />

    <!-- Theme css -->
    <link href="dist/css/AdminLTE.min.css" rel="stylesheet" type="text/css" />
    <link href="dist/css/skins/_all-skins.min.css" rel="stylesheet" type="text/css" />

    <!-- Post Validation CSS -->
    <link href="css/postvalidationcss.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="../css/validate.css">
    
    <style>
        .phonepe-header {
            background: linear-gradient(135deg, #5f27cd 0%, #7c4dff 100%);
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }
        .status-approved { background: #d4edda; color: #155724; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-disabled { background: #f8d7da; color: #721c24; }
        .btn-phonepe {
            background: linear-gradient(135deg, #5f27cd 0%, #7c4dff 100%);
            color: white;
            border: none;
        }
        .btn-phonepe:hover {
            background: linear-gradient(135deg, #4a1fa8 0%, #6a3de8 100%);
            color: white;
        }
    </style>
</head>
<body class="skin-blue">
    <!-- Header & Menu -->
    <?php include "page-part/header.php"; ?>
    <?php include "page-part/left_panel.php"; ?>
    <!-- /. Header & Menu -->

    <div class="content-wrapper">
        <section class="content-header">
            <h1 class="lightGrey">
                <i class="fa fa-credit-card"></i> PhonePe Payment Gateway Settings
                <small>Configure PhonePe payment integration</small>
            </h1>
            <ol class="breadcrumb">
                <li><a href="dashboard"><i class="fa fa-home"></i> Home</a></li>
                <li><a href="PaymentOption"><i class="fa fa-credit-card"></i> Payment Options</a></li>
                <li class="active">PhonePe Settings</li>
            </ol>
        </section>

        <section class="content">
            <!-- Messages -->
            <?php if ($message): ?>
            <div class="alert alert-<?php echo $message_type == 'success' ? 'success' : 'danger'; ?> alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                <h4><i class="icon fa fa-<?php echo $message_type == 'success' ? 'check' : 'ban'; ?>"></i>
                    <?php echo $message_type == 'success' ? 'Success!' : 'Error!'; ?>
                </h4>
                <?php echo htmlspecialchars($message); ?>
            </div>
            <?php endif; ?>

            <!-- Current Status Box -->
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title"><i class="fa fa-info-circle"></i> Current PhonePe Status</h3>
                </div>
                <div class="box-body">
                    <div class="row">
                        <div class="col-md-3">
                            <strong>Status:</strong><br>
                            <?php if ($current_config): ?>
                                <span class="label label-<?php echo $current_config->status == 'APPROVED' ? 'success' : 'warning'; ?>">
                                    <?php echo $current_config->status == 'APPROVED' ? 'Active' : 'Inactive'; ?>
                                </span>
                            <?php else: ?>
                                <span class="label label-default">Not Configured</span>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-3">
                            <strong>Environment:</strong><br>
                            <?php if ($current_config): ?>
                                <span class="label label-<?php echo $current_config->phonepe_is_production == 'YES' ? 'danger' : 'info'; ?>">
                                    <?php echo $current_config->phonepe_is_production == 'YES' ? 'Production' : 'Sandbox'; ?>
                                </span>
                            <?php else: ?>
                                <span class="label label-default">Not Set</span>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-3">
                            <strong>Merchant ID:</strong><br>
                            <?php echo $current_config && $current_config->phonepe_merchant_id ? substr($current_config->phonepe_merchant_id, 0, 8) . '...' : 'Not Set'; ?>
                        </div>
                        <div class="col-md-3">
                            <strong>Salt Index:</strong><br>
                            <?php echo $current_config && $current_config->phonepe_salt_index ? $current_config->phonepe_salt_index : 'Not Set'; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Setup Instructions Box -->
            <div class="box box-info">
                <div class="box-header with-border">
                    <h3 class="box-title"><i class="fa fa-lightbulb-o"></i> PhonePe Setup Instructions</h3>
                </div>
                <div class="box-body">
                    <ol>
                        <li>Visit <a href="https://developer.phonepe.com" target="_blank">PhonePe Developer Portal</a></li>
                        <li>Create a merchant account and get your credentials</li>
                        <li>Get your Merchant ID, Salt Key, and Salt Index</li>
                        <li>Configure the settings below</li>
                        <li>Test the integration before going live</li>
                    </ol>
                </div>
            </div>
            
            <!-- Configuration Form Box -->
            <div class="box box-success">
                <div class="box-header with-border">
                    <h3 class="box-title"><i class="fa fa-cog"></i> PhonePe Configuration</h3>
                </div>
                <div class="box-body">
                    <form method="POST" id="phonepe-config-form">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="merchant_id"><i class="fa fa-building"></i> Merchant ID *</label>
                                    <input type="text" class="form-control" id="merchant_id" name="merchant_id"
                                           value="<?php echo htmlspecialchars($current_config->phonepe_merchant_id ?? ''); ?>"
                                           placeholder="Enter your PhonePe Merchant ID" required>
                                    <small class="text-muted">Your unique merchant identifier from PhonePe</small>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="salt_index"><i class="fa fa-key"></i> Salt Index *</label>
                                    <input type="number" class="form-control" id="salt_index" name="salt_index"
                                           value="<?php echo htmlspecialchars($current_config->phonepe_salt_index ?? '1'); ?>"
                                           placeholder="1" min="1" required>
                                    <small class="text-muted">Usually 1 for most merchants</small>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="salt_key"><i class="fa fa-lock"></i> Salt Key *</label>
                            <input type="password" class="form-control" id="salt_key" name="salt_key"
                                   value="<?php echo htmlspecialchars($current_config->phonepe_salt_key ?? ''); ?>"
                                   placeholder="Enter your PhonePe Salt Key" required>
                            <small class="text-muted">Keep this secret and secure</small>
                        </div>
                
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="is_production"><i class="fa fa-server"></i> Environment</label>
                                    <select class="form-control" id="is_production" name="is_production">
                                        <option value="NO" <?php echo ($current_config->phonepe_is_production ?? '') == 'NO' ? 'selected' : ''; ?>>
                                            Sandbox (Testing)
                                        </option>
                                        <option value="YES" <?php echo ($current_config->phonepe_is_production ?? '') == 'YES' ? 'selected' : ''; ?>>
                                            Production (Live)
                                        </option>
                                    </select>
                                    <small class="text-muted">Use Sandbox for testing, Production for live payments</small>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status"><i class="fa fa-toggle-on"></i> Status</label>
                                    <select class="form-control" id="status" name="status">
                                        <option value="APPROVED" <?php echo ($current_config->status ?? '') == 'APPROVED' ? 'selected' : ''; ?>>
                                            Enabled
                                        </option>
                                        <option value="PENDING" <?php echo ($current_config->status ?? '') == 'PENDING' ? 'selected' : ''; ?>>
                                            Disabled
                                        </option>
                                    </select>
                                    <small class="text-muted">Enable/disable PhonePe payments</small>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" name="update_phonepe_settings" class="btn btn-phonepe">
                                <i class="fa fa-save"></i> Save PhonePe Settings
                            </button>

                            <?php if ($current_config && $current_config->status == 'APPROVED'): ?>
                            <button type="submit" name="test_phonepe" class="btn btn-success" style="margin-left: 10px;">
                                <i class="fa fa-flask"></i> Test Integration
                            </button>
                            <?php endif; ?>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Webhook Configuration Box -->
            <div class="box box-warning">
                <div class="box-header with-border">
                    <h3 class="box-title"><i class="fa fa-link"></i> Webhook Configuration</h3>
                </div>
                <div class="box-body">
                    <p>Configure these URLs in your PhonePe merchant dashboard:</p>

                    <div class="form-group">
                        <label>Redirect URL (Success/Failure):</label>
                        <div class="well well-sm" style="font-family: monospace; font-size: 12px;">
                            <?php echo 'https://' . $_SERVER['HTTP_HOST'] . '/phonepe_callback.php'; ?>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Webhook URL (Server-to-Server):</label>
                        <div class="well well-sm" style="font-family: monospace; font-size: 12px;">
                            <?php echo 'https://' . $_SERVER['HTTP_HOST'] . '/phonepe_webhook.php'; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Integration Guide -->
            <div class="warning-box">
                <h4><i class="fa fa-exclamation-triangle"></i> Important Notes</h4>
                <ul>
                    <li><strong>Security:</strong> Never share your Salt Key publicly</li>
                    <li><strong>Testing:</strong> Always test in Sandbox before going live</li>
                    <li><strong>Webhooks:</strong> Configure webhook URLs in PhonePe dashboard</li>
                    <li><strong>SSL:</strong> Ensure your website has valid SSL certificate</li>
                    <li><strong>Compliance:</strong> Follow PhonePe's integration guidelines</li>
                </ul>
            </div>
            
            <!-- Documentation Links -->
            <div class="info-box">
                <h4><i class="fa fa-book"></i> Documentation & Support</h4>
                <ul>
                    <li><a href="https://developer.phonepe.com/v1/docs/introduction-pg" target="_blank">PhonePe API Documentation</a></li>
                    <li><a href="https://developer.phonepe.com/v1/docs/payment-gateway-integration" target="_blank">Integration Guide</a></li>
                    <li><a href="https://developer.phonepe.com/v1/docs/webhooks" target="_blank">Webhook Documentation</a></li>
                    <li><a href="mailto:<EMAIL>">PhonePe Support</a></li>
                </ul>
            </div>
            
            <!-- Quick Actions Box -->
            <div class="box box-default">
                <div class="box-header with-border">
                    <h3 class="box-title"><i class="fa fa-external-link"></i> Quick Actions</h3>
                </div>
                <div class="box-body">
                    <div class="btn-group">
                        <a href="PaymentOption.php" class="btn btn-default">
                            <i class="fa fa-arrow-left"></i> Back to Payment Options
                        </a>
                        <a href="../test_phonepe_integration.php" target="_blank" class="btn btn-info">
                            <i class="fa fa-flask"></i> Test Integration
                        </a>
                        <a href="../phonepe_production_deployment.php" target="_blank" class="btn btn-warning">
                            <i class="fa fa-rocket"></i> Production Guide
                        </a>
                        <a href="dashboard.php" class="btn btn-primary">
                            <i class="fa fa-home"></i> Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Footer -->
    <?php include "page-part/footer.php"; ?>

    <!-- jQuery -->
    <script src="plugins/jQuery/jQuery-2.1.3.min.js"></script>
    <!-- Bootstrap -->
    <script src="bootstrap/js/bootstrap.min.js" type="text/javascript"></script>
    <!-- AdminLTE App -->
    <script src="dist/js/app.min.js" type="text/javascript"></script>
    <script>
        $(document).ready(function() {
            // Form validation
            $('#phonepe-config-form').on('submit', function(e) {
                var merchantId = $('#merchant_id').val().trim();
                var saltKey = $('#salt_key').val().trim();
                
                if (!merchantId || !saltKey) {
                    e.preventDefault();
                    alert('Please fill in all required fields (Merchant ID and Salt Key)');
                    return false;
                }
                
                if (saltKey.length < 10) {
                    e.preventDefault();
                    alert('Salt Key seems too short. Please verify your credentials.');
                    return false;
                }
            });
            
            // Toggle password visibility
            $('<button type="button" class="btn btn-sm btn-outline-secondary" style="margin-left: 10px;">Show</button>')
                .insertAfter('#salt_key')
                .on('click', function() {
                    var input = $('#salt_key');
                    var type = input.attr('type') === 'password' ? 'text' : 'password';
                    input.attr('type', type);
                    $(this).text(type === 'password' ? 'Show' : 'Hide');
                });
        });
    </script>
</body>
</html>
