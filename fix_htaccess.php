<?php
/**
 * .htaccess Fix Script
 * This script helps you safely update your .htaccess files
 */

echo "<!DOCTYPE html>
<html>
<head>
    <title>.htaccess Fix Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: green; background: #e8f5e8; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid green; }
        .error { color: red; background: #ffe8e8; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid red; }
        .warning { color: orange; background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid orange; }
        .info { color: blue; background: #e8f0ff; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid blue; }
        .step { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background: #fafafa; }
        .btn { display: inline-block; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .btn:hover { background: #005a87; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
        .file-status { display: flex; align-items: center; margin: 10px 0; }
        .status-icon { margin-right: 10px; font-size: 18px; }
    </style>
</head>
<body>";

echo "<div class='container'>";
echo "<h1>🔧 .htaccess Fix Tool</h1>";
echo "<p>This tool will help you fix .htaccess issues in your matrimony site.</p>";

// Check current .htaccess files
echo "<div class='step'>";
echo "<h2>📋 Current .htaccess Status</h2>";

$root_htaccess = '.htaccess';
$admin_htaccess = 'premium_admin/.htaccess';
$root_backup = '.htaccess.backup';
$admin_backup = 'premium_admin/.htaccess.backup';

echo "<div class='file-status'>";
if (file_exists($root_htaccess)) {
    echo "<span class='status-icon'>✅</span> Root .htaccess exists (" . filesize($root_htaccess) . " bytes)";
} else {
    echo "<span class='status-icon'>❌</span> Root .htaccess missing";
}
echo "</div>";

echo "<div class='file-status'>";
if (file_exists($admin_htaccess)) {
    echo "<span class='status-icon'>✅</span> Admin .htaccess exists (" . filesize($admin_htaccess) . " bytes)";
} else {
    echo "<span class='status-icon'>❌</span> Admin .htaccess missing";
}
echo "</div>";

echo "<div class='file-status'>";
if (file_exists($root_backup)) {
    echo "<span class='status-icon'>💾</span> Root backup exists";
} else {
    echo "<span class='status-icon'>⚠️</span> No root backup found";
}
echo "</div>";

echo "</div>";

// Handle form submissions
if (isset($_POST['action'])) {
    $action = $_POST['action'];
    
    echo "<div class='step'>";
    echo "<h2>🚀 Action Results</h2>";
    
    switch ($action) {
        case 'backup':
            // Create backups
            if (file_exists($root_htaccess)) {
                if (copy($root_htaccess, $root_backup)) {
                    echo "<div class='success'>✅ Root .htaccess backed up successfully</div>";
                } else {
                    echo "<div class='error'>❌ Failed to backup root .htaccess</div>";
                }
            }
            
            if (file_exists($admin_htaccess)) {
                if (copy($admin_htaccess, $admin_backup)) {
                    echo "<div class='success'>✅ Admin .htaccess backed up successfully</div>";
                } else {
                    echo "<div class='error'>❌ Failed to backup admin .htaccess</div>";
                }
            }
            break;
            
        case 'apply_new':
            // Apply new .htaccess files
            if (file_exists('.htaccess.new')) {
                if (copy('.htaccess.new', $root_htaccess)) {
                    echo "<div class='success'>✅ New root .htaccess applied successfully</div>";
                } else {
                    echo "<div class='error'>❌ Failed to apply new root .htaccess</div>";
                }
            }
            
            if (file_exists('premium_admin/.htaccess.new')) {
                if (copy('premium_admin/.htaccess.new', $admin_htaccess)) {
                    echo "<div class='success'>✅ New admin .htaccess applied successfully</div>";
                } else {
                    echo "<div class='error'>❌ Failed to apply new admin .htaccess</div>";
                }
            }
            break;
            
        case 'restore':
            // Restore from backup
            if (file_exists($root_backup)) {
                if (copy($root_backup, $root_htaccess)) {
                    echo "<div class='success'>✅ Root .htaccess restored from backup</div>";
                } else {
                    echo "<div class='error'>❌ Failed to restore root .htaccess</div>";
                }
            }
            
            if (file_exists($admin_backup)) {
                if (copy($admin_backup, $admin_htaccess)) {
                    echo "<div class='success'>✅ Admin .htaccess restored from backup</div>";
                } else {
                    echo "<div class='error'>❌ Failed to restore admin .htaccess</div>";
                }
            }
            break;
            
        case 'test':
            // Test current configuration
            echo "<div class='info'>🧪 Testing current .htaccess configuration...</div>";
            
            // Test if mod_rewrite is working
            if (function_exists('apache_get_modules')) {
                $modules = apache_get_modules();
                if (in_array('mod_rewrite', $modules)) {
                    echo "<div class='success'>✅ mod_rewrite is enabled</div>";
                } else {
                    echo "<div class='error'>❌ mod_rewrite is not enabled</div>";
                }
            } else {
                echo "<div class='warning'>⚠️ Cannot check mod_rewrite status (function not available)</div>";
            }
            
            // Test if .htaccess is being read
            if (file_exists($root_htaccess)) {
                $content = file_get_contents($root_htaccess);
                if (strpos($content, 'RewriteEngine') !== false) {
                    echo "<div class='success'>✅ .htaccess contains rewrite rules</div>";
                } else {
                    echo "<div class='warning'>⚠️ .htaccess doesn't contain rewrite rules</div>";
                }
            }
            
            // Test PHP settings
            echo "<div class='info'>📊 Current PHP Settings:</div>";
            echo "<pre>";
            echo "Memory Limit: " . ini_get('memory_limit') . "\n";
            echo "Upload Max Filesize: " . ini_get('upload_max_filesize') . "\n";
            echo "Post Max Size: " . ini_get('post_max_size') . "\n";
            echo "Max Execution Time: " . ini_get('max_execution_time') . "\n";
            echo "</pre>";
            break;
    }
    
    echo "</div>";
}

// Action buttons
echo "<div class='step'>";
echo "<h2>🎯 Available Actions</h2>";

echo "<form method='post' style='display: inline;'>";
echo "<input type='hidden' name='action' value='backup'>";
echo "<button type='submit' class='btn btn-warning'>💾 Create Backup</button>";
echo "</form>";

echo "<form method='post' style='display: inline;'>";
echo "<input type='hidden' name='action' value='apply_new'>";
echo "<button type='submit' class='btn btn-success'>🚀 Apply New .htaccess</button>";
echo "</form>";

echo "<form method='post' style='display: inline;'>";
echo "<input type='hidden' name='action' value='restore'>";
echo "<button type='submit' class='btn btn-danger'>🔄 Restore Backup</button>";
echo "</form>";

echo "<form method='post' style='display: inline;'>";
echo "<input type='hidden' name='action' value='test'>";
echo "<button type='submit' class='btn'>🧪 Test Configuration</button>";
echo "</form>";

echo "</div>";

// Show differences
echo "<div class='step'>";
echo "<h2>📊 What's Different in New .htaccess</h2>";

echo "<div class='info'>";
echo "<h3>🔒 Security Improvements:</h3>";
echo "<ul>";
echo "<li>Added security headers (X-Frame-Options, X-XSS-Protection, etc.)</li>";
echo "<li>Block access to sensitive files (.env, config files, backups)</li>";
echo "<li>Remove server signatures</li>";
echo "<li>Enhanced directory protection</li>";
echo "</ul>";

echo "<h3>⚡ Performance Improvements:</h3>";
echo "<ul>";
echo "<li>Gzip compression for faster loading</li>";
echo "<li>Browser caching for static assets</li>";
echo "<li>Optimized PHP settings</li>";
echo "</ul>";

echo "<h3>🔧 Fixed Issues:</h3>";
echo "<ul>";
echo "<li>Commented out HTTPS redirect for local development</li>";
echo "<li>Improved URL rewriting logic</li>";
echo "<li>Better error handling</li>";
echo "<li>Conditional redirects (only for production)</li>";
echo "</ul>";
echo "</div>";

echo "</div>";

// Recommendations
echo "<div class='step'>";
echo "<h2>💡 Recommendations</h2>";

echo "<div class='warning'>";
echo "<h3>⚠️ Before Applying Changes:</h3>";
echo "<ol>";
echo "<li><strong>Create Backup:</strong> Always backup current .htaccess files first</li>";
echo "<li><strong>Test Locally:</strong> Test changes on local/staging environment</li>";
echo "<li><strong>Monitor:</strong> Watch for any errors after applying changes</li>";
echo "<li><strong>Have Rollback Plan:</strong> Keep backup files ready for quick restore</li>";
echo "</ol>";
echo "</div>";

echo "<div class='success'>";
echo "<h3>✅ After Applying Changes:</h3>";
echo "<ol>";
echo "<li>Test your website functionality</li>";
echo "<li>Check admin panel access</li>";
echo "<li>Verify SMS and OTP functionality</li>";
echo "<li>Test file uploads and downloads</li>";
echo "<li>Monitor server error logs</li>";
echo "</ol>";
echo "</div>";

echo "</div>";

echo "<div class='step'>";
echo "<h2>🆘 Troubleshooting</h2>";

echo "<div class='info'>";
echo "<h3>Common Issues & Solutions:</h3>";
echo "<ul>";
echo "<li><strong>500 Internal Server Error:</strong> Restore backup and check server error logs</li>";
echo "<li><strong>Redirect Loops:</strong> Comment out redirect rules in .htaccess</li>";
echo "<li><strong>Admin Panel Not Accessible:</strong> Check admin .htaccess file</li>";
echo "<li><strong>CSS/JS Not Loading:</strong> Check file permissions and caching rules</li>";
echo "</ul>";

echo "<h3>Emergency Restore:</h3>";
echo "<p>If something goes wrong, you can manually restore by:</p>";
echo "<ol>";
echo "<li>Rename current .htaccess to .htaccess.broken</li>";
echo "<li>Rename .htaccess.backup to .htaccess</li>";
echo "<li>Or delete .htaccess entirely to disable URL rewriting</li>";
echo "</ol>";
echo "</div>";

echo "</div>";

echo "<p><a href='premium_admin/dashboard.php' class='btn'>← Back to Admin Dashboard</a></p>";

echo "</div>";
echo "</body></html>";
?>
