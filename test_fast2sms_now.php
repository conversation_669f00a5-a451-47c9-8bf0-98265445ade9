<?php
/**
 * Quick Fast2SMS Test
 * Test your current Fast2SMS configuration
 */

echo "<!DOCTYPE html>
<html>
<head>
    <title>Fast2SMS Quick Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: green; background: #e8f5e8; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid green; }
        .error { color: red; background: #ffe8e8; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid red; }
        .warning { color: orange; background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid orange; }
        .info { color: blue; background: #e8f0ff; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid blue; }
        .btn { display: inline-block; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; border: none; border-radius: 5px; margin: 5px; cursor: pointer; }
        .btn:hover { background: #005a87; }
        .btn-success { background: #28a745; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>";

echo "<div class='container'>";
echo "<h1>📱 Fast2SMS Quick Test</h1>";

// Test current API
$api_key = "r2oeVHLQzpadwlNAGiEXtBfguK1IWS7YxZ58ROqn3DhMFvy9UbjN4C6iqf59nFPW8yD0wuZslmAJRtOr";
$test_mobile = "9999999999"; // Test number
$test_otp = "1234";

echo "<div class='info'>";
echo "<h2>🔍 Testing Current Configuration</h2>";
echo "<strong>API Key:</strong> " . substr($api_key, 0, 20) . "...<br>";
echo "<strong>Test Mobile:</strong> $test_mobile<br>";
echo "<strong>Test OTP:</strong> $test_otp<br>";
echo "</div>";

// Test OTP Route
echo "<div class='info'>";
echo "<h2>🧪 Testing OTP Route</h2>";
$otp_url = "https://www.fast2sms.com/dev/bulkV2?authorization=$api_key&route=otp&variables_values=$test_otp&flash=0&numbers=$test_mobile";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $otp_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "<strong>HTTP Code:</strong> $http_code<br>";
if ($error) {
    echo "<div class='error'>❌ cURL Error: $error</div>";
} else {
    echo "<strong>Response:</strong> <pre>$response</pre>";
    
    // Parse response
    $response_data = json_decode($response, true);
    if ($response_data) {
        if (isset($response_data['return']) && $response_data['return'] == true) {
            echo "<div class='success'>✅ OTP Route: SUCCESS! Your API is working.</div>";
        } else {
            $error_code = isset($response_data['code']) ? $response_data['code'] : 'Unknown';
            $error_message = isset($response_data['message']) ? $response_data['message'] : 'Unknown error';
            echo "<div class='error'>❌ OTP Route Failed</div>";
            echo "<strong>Error Code:</strong> $error_code<br>";
            echo "<strong>Error Message:</strong> $error_message<br>";
            
            if ($error_code == '996') {
                echo "<div class='warning'>";
                echo "<h3>⚠️ Website Verification Required</h3>";
                echo "<p>Your Fast2SMS account needs website verification to use OTP route.</p>";
                echo "<strong>Solution:</strong>";
                echo "<ol>";
                echo "<li>Login to <a href='https://www.fast2sms.com/login' target='_blank'>Fast2SMS Dashboard</a></li>";
                echo "<li>Go to 'OTP Message' menu</li>";
                echo "<li>Complete website verification</li>";
                echo "<li>Or use DLT route instead</li>";
                echo "</ol>";
                echo "</div>";
            }
        }
    }
}
echo "</div>";

// Test DLT Route
echo "<div class='info'>";
echo "<h2>🧪 Testing DLT Route</h2>";
$message = urlencode("Your OTP is $test_otp. Do not share with anyone.");
$dlt_url = "https://www.fast2sms.com/dev/bulkV2?authorization=$api_key&route=dlt&sender_id=FSTSMS&message=$message&variables_values=$test_otp&flash=0&numbers=$test_mobile";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $dlt_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "<strong>HTTP Code:</strong> $http_code<br>";
if ($error) {
    echo "<div class='error'>❌ cURL Error: $error</div>";
} else {
    echo "<strong>Response:</strong> <pre>$response</pre>";
    
    // Parse response
    $response_data = json_decode($response, true);
    if ($response_data) {
        if (isset($response_data['return']) && $response_data['return'] == true) {
            echo "<div class='success'>✅ DLT Route: SUCCESS! You can use DLT route.</div>";
        } else {
            $error_code = isset($response_data['code']) ? $response_data['code'] : 'Unknown';
            $error_message = isset($response_data['message']) ? $response_data['message'] : 'Unknown error';
            echo "<div class='error'>❌ DLT Route Failed</div>";
            echo "<strong>Error Code:</strong> $error_code<br>";
            echo "<strong>Error Message:</strong> $error_message<br>";
            
            if ($error_code == '406') {
                echo "<div class='warning'>";
                echo "<h3>⚠️ Invalid Sender ID</h3>";
                echo "<p>You need to register and get approval for a sender ID in Fast2SMS DLT section.</p>";
                echo "</div>";
            }
        }
    }
}
echo "</div>";

// Recommendations
echo "<div class='info'>";
echo "<h2>💡 Recommendations</h2>";
echo "<div class='warning'>";
echo "<h3>🚀 Quick Solutions:</h3>";
echo "<ol>";
echo "<li><strong>For OTP Route:</strong> Complete website verification in Fast2SMS dashboard</li>";
echo "<li><strong>For DLT Route:</strong> Register and get approved sender ID</li>";
echo "<li><strong>Alternative:</strong> Switch to MSG91 or Textlocal for immediate SMS</li>";
echo "</ol>";
echo "</div>";

echo "<h3>🔧 Admin Panel Configuration:</h3>";
echo "<a href='premium_admin/SiteSMSSettings.php' class='btn btn-success'>Configure SMS Settings</a>";
echo "<a href='premium_admin/fix_admin_session.php' class='btn'>Fix Admin Session</a>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
