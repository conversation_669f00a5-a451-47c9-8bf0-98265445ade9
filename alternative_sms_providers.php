<?php
/**
 * Alternative SMS Providers Configuration
 * Use this while waiting for Fast2SMS verification
 */

// Get parameters
$mobile = isset($_GET['mobile']) ? $_GET['mobile'] : '';
$otp = isset($_GET['otp']) ? $_GET['otp'] : '';
$provider = isset($_GET['provider']) ? $_GET['provider'] : 'msg91';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Alternative SMS Providers</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: green; background: #e8f5e8; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid green; }
        .error { color: red; background: #ffe8e8; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid red; }
        .warning { color: orange; background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid orange; }
        .info { color: blue; background: #e8f0ff; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid blue; }
        .btn { display: inline-block; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; border: none; border-radius: 5px; margin: 5px; cursor: pointer; }
        .btn:hover { background: #005a87; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: 5px; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
    </style>
</head>
<body>";

echo "<div class='container'>";
echo "<h1>📱 Alternative SMS Providers</h1>";

echo "<div class='warning'>";
echo "<h3>⚠️ Fast2SMS Issue:</h3>";
echo "<p>While waiting for Fast2SMS website verification, you can use these alternative SMS providers for immediate functionality.</p>";
echo "</div>";

// Handle SMS sending
if ($mobile && $otp && $provider) {
    echo "<div class='info'>";
    echo "<h3>🧪 Testing SMS with $provider</h3>";
    echo "<strong>Mobile:</strong> $mobile<br>";
    echo "<strong>OTP:</strong> $otp<br>";
    echo "<strong>Provider:</strong> " . strtoupper($provider) . "<br>";
    echo "</div>";
    
    $success = false;
    $response = '';
    $error_message = '';
    
    if ($provider == 'msg91') {
        // MSG91 Configuration (Demo - Replace with your credentials)
        $auth_key = "YOUR_MSG91_AUTH_KEY"; // Replace with your MSG91 auth key
        $sender_id = "MATRIM"; // Replace with your approved sender ID
        $message = urlencode("Your OTP is $otp. Do not share with anyone. -Team Matrimony");
        
        $url = "https://control.msg91.com/api/sendhttp.php?authkey=$auth_key&mobiles=$mobile&message=$message&sender=$sender_id&route=4&country=91";
        
        echo "<div class='info'>";
        echo "<h3>📡 MSG91 API Request:</h3>";
        echo "<strong>URL:</strong><br><pre>$url</pre>";
        echo "</div>";
        
        // Test with demo response (since we don't have real credentials)
        if ($auth_key == "YOUR_MSG91_AUTH_KEY") {
            $response = "Demo Mode: MSG91 API would be called with above URL";
            $error_message = "Please update MSG91 credentials in the code";
        } else {
            // Real API call
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($http_code == 200 && strpos($response, 'success') !== false) {
                $success = true;
            }
        }
        
    } elseif ($provider == 'textlocal') {
        // Textlocal Configuration (Demo - Replace with your credentials)
        $api_key = "YOUR_TEXTLOCAL_API_KEY"; // Replace with your Textlocal API key
        $sender_id = "MATRIM"; // Replace with your approved sender ID
        $message = urlencode("Your OTP is $otp. Do not share with anyone.");
        
        $url = "https://api.textlocal.in/send/?apikey=$api_key&numbers=$mobile&message=$message&sender=$sender_id";
        
        echo "<div class='info'>";
        echo "<h3>📡 Textlocal API Request:</h3>";
        echo "<strong>URL:</strong><br><pre>$url</pre>";
        echo "</div>";
        
        // Test with demo response (since we don't have real credentials)
        if ($api_key == "YOUR_TEXTLOCAL_API_KEY") {
            $response = "Demo Mode: Textlocal API would be called with above URL";
            $error_message = "Please update Textlocal credentials in the code";
        } else {
            // Real API call
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            $response_data = json_decode($response, true);
            if ($response_data && isset($response_data['status']) && $response_data['status'] == 'success') {
                $success = true;
            }
        }
        
    } elseif ($provider == 'twilio') {
        // Twilio Configuration (Demo)
        $account_sid = "YOUR_TWILIO_ACCOUNT_SID";
        $auth_token = "YOUR_TWILIO_AUTH_TOKEN";
        $from_number = "YOUR_TWILIO_PHONE_NUMBER";
        
        echo "<div class='info'>";
        echo "<h3>📡 Twilio API (Premium Option):</h3>";
        echo "<p>Twilio is a premium SMS service with excellent delivery rates.</p>";
        echo "<strong>Account SID:</strong> $account_sid<br>";
        echo "<strong>From Number:</strong> $from_number<br>";
        echo "</div>";
        
        $response = "Demo Mode: Twilio API integration available";
        $error_message = "Please update Twilio credentials for real usage";
    }
    
    // Display results
    if ($success) {
        echo "<div class='success'>";
        echo "<h3>✅ SMS Sent Successfully!</h3>";
        echo "<strong>Response:</strong><br><pre>$response</pre>";
        echo "</div>";
    } else {
        echo "<div class='error'>";
        echo "<h3>❌ SMS Failed</h3>";
        echo "<strong>Response:</strong><br><pre>$response</pre>";
        if ($error_message) {
            echo "<strong>Note:</strong> $error_message<br>";
        }
        echo "</div>";
    }
}

// Test form
echo "<div class='info'>";
echo "<h2>🧪 Test Alternative SMS Providers</h2>";
echo "<form method='get'>";
echo "<div class='form-group'>";
echo "<label>Mobile Number (10 digits):</label>";
echo "<input type='text' name='mobile' value='$mobile' placeholder='**********' required>";
echo "</div>";
echo "<div class='form-group'>";
echo "<label>OTP (4-6 digits):</label>";
echo "<input type='text' name='otp' value='" . ($otp ?: rand(1000, 9999)) . "' required>";
echo "</div>";
echo "<div class='form-group'>";
echo "<label>SMS Provider:</label>";
echo "<select name='provider'>";
echo "<option value='msg91'" . ($provider == 'msg91' ? ' selected' : '') . ">MSG91 (Recommended)</option>";
echo "<option value='textlocal'" . ($provider == 'textlocal' ? ' selected' : '') . ">Textlocal</option>";
echo "<option value='twilio'" . ($provider == 'twilio' ? ' selected' : '') . ">Twilio (Premium)</option>";
echo "</select>";
echo "</div>";
echo "<button type='submit' class='btn btn-success'>Test SMS</button>";
echo "</form>";
echo "</div>";

// Provider comparison
echo "<div class='info'>";
echo "<h2>📊 SMS Provider Comparison</h2>";
echo "<table border='1' style='width: 100%; border-collapse: collapse; margin: 15px 0;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='padding: 10px; text-align: left;'>Provider</th>";
echo "<th style='padding: 10px; text-align: left;'>Setup Time</th>";
echo "<th style='padding: 10px; text-align: left;'>Cost</th>";
echo "<th style='padding: 10px; text-align: left;'>Delivery Rate</th>";
echo "<th style='padding: 10px; text-align: left;'>Support</th>";
echo "</tr>";
echo "<tr>";
echo "<td style='padding: 10px;'><strong>MSG91</strong></td>";
echo "<td style='padding: 10px;'>5-10 minutes</td>";
echo "<td style='padding: 10px;'>₹0.15-0.25/SMS</td>";
echo "<td style='padding: 10px;'>95-98%</td>";
echo "<td style='padding: 10px;'>Good</td>";
echo "</tr>";
echo "<tr>";
echo "<td style='padding: 10px;'><strong>Textlocal</strong></td>";
echo "<td style='padding: 10px;'>5-10 minutes</td>";
echo "<td style='padding: 10px;'>₹0.20-0.30/SMS</td>";
echo "<td style='padding: 10px;'>94-97%</td>";
echo "<td style='padding: 10px;'>Good</td>";
echo "</tr>";
echo "<tr>";
echo "<td style='padding: 10px;'><strong>Twilio</strong></td>";
echo "<td style='padding: 10px;'>10-15 minutes</td>";
echo "<td style='padding: 10px;'>₹0.50-1.00/SMS</td>";
echo "<td style='padding: 10px;'>98-99%</td>";
echo "<td style='padding: 10px;'>Excellent</td>";
echo "</tr>";
echo "<tr>";
echo "<td style='padding: 10px;'><strong>Fast2SMS</strong></td>";
echo "<td style='padding: 10px;'>24-48 hours (verification)</td>";
echo "<td style='padding: 10px;'>₹0.10-0.20/SMS</td>";
echo "<td style='padding: 10px;'>92-95%</td>";
echo "<td style='padding: 10px;'>Average</td>";
echo "</tr>";
echo "</table>";
echo "</div>";

// Setup instructions
echo "<div class='info'>";
echo "<h2>🛠️ Quick Setup Instructions</h2>";

echo "<h3>MSG91 Setup (Recommended):</h3>";
echo "<ol>";
echo "<li>Go to <a href='https://msg91.com' target='_blank'>MSG91.com</a></li>";
echo "<li>Sign up for free account</li>";
echo "<li>Verify your mobile number</li>";
echo "<li>Get your Auth Key from dashboard</li>";
echo "<li>Register a sender ID (e.g., 'MATRIM')</li>";
echo "<li>Update the code with your credentials</li>";
echo "</ol>";

echo "<h3>Textlocal Setup:</h3>";
echo "<ol>";
echo "<li>Go to <a href='https://www.textlocal.in' target='_blank'>Textlocal.in</a></li>";
echo "<li>Create account and verify</li>";
echo "<li>Get API key from settings</li>";
echo "<li>Register sender ID</li>";
echo "<li>Update the code with your credentials</li>";
echo "</ol>";
echo "</div>";

// Code examples
echo "<div class='info'>";
echo "<h2>💻 Integration Code Examples</h2>";

echo "<h3>MSG91 Integration:</h3>";
echo "<pre>";
echo htmlspecialchars('<?php
// MSG91 SMS Function
function sendSMS_MSG91($mobile, $otp) {
    $auth_key = "YOUR_MSG91_AUTH_KEY";
    $sender_id = "MATRIM";
    $message = urlencode("Your OTP is $otp. Do not share with anyone.");
    
    $url = "https://control.msg91.com/api/sendhttp.php?authkey=$auth_key&mobiles=$mobile&message=$message&sender=$sender_id&route=4&country=91";
    
    $response = file_get_contents($url);
    return $response;
}
?>');
echo "</pre>";

echo "<h3>Textlocal Integration:</h3>";
echo "<pre>";
echo htmlspecialchars('<?php
// Textlocal SMS Function
function sendSMS_Textlocal($mobile, $otp) {
    $api_key = "YOUR_TEXTLOCAL_API_KEY";
    $sender_id = "MATRIM";
    $message = urlencode("Your OTP is $otp. Do not share with anyone.");
    
    $url = "https://api.textlocal.in/send/?apikey=$api_key&numbers=$mobile&message=$message&sender=$sender_id";
    
    $response = file_get_contents($url);
    return $response;
}
?>');
echo "</pre>";
echo "</div>";

echo "<div class='warning'>";
echo "<h3>🔄 Next Steps:</h3>";
echo "<ol>";
echo "<li><strong>Choose a provider</strong> from the options above</li>";
echo "<li><strong>Sign up and get credentials</strong> (API key, sender ID)</li>";
echo "<li><strong>Update your mobile-apis.php</strong> with new provider details</li>";
echo "<li><strong>Test SMS delivery</strong> using the form above</li>";
echo "<li><strong>Update admin panel</strong> SMS settings</li>";
echo "</ol>";
echo "</div>";

echo "<div class='text-center' style='margin-top: 30px;'>";
echo "<a href='premium_admin/SiteSMSSettings.php' class='btn btn-success'>Update SMS Settings</a>";
echo "<a href='fast2sms_verification_guide.php' class='btn btn-warning'>Fast2SMS Verification Guide</a>";
echo "<a href='test_fast2sms_otp_route.php' class='btn btn-danger'>Test Current SMS</a>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
