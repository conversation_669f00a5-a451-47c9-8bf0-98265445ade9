<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Fast2SMS Verification Guide</title>
    <link href="css/bootstrap.css" rel="stylesheet">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: green; background: #e8f5e8; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid green; }
        .error { color: red; background: #ffe8e8; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid red; }
        .warning { color: orange; background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid orange; }
        .info { color: blue; background: #e8f0ff; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid blue; }
        .btn { display: inline-block; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; border: none; border-radius: 5px; margin: 5px; cursor: pointer; }
        .btn:hover { background: #005a87; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; }
        .step { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007cba; }
        .step h3 { color: #007cba; margin-top: 0; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Fast2SMS Website Verification Guide</h1>
        
        <div class="error">
            <h3>❌ Current Issue:</h3>
            <p><strong>Error 996:</strong> "Before using OTP Message API, complete website verification. Visit OTP Message menu or use DLT SMS API."</p>
            <p><strong>Your API Key:</strong> r2oeVHLQzpadwlNAGiEXtBfguK1IWS7YxZ58ROqn3DhMFvy9UbjN4C6iqf59nFPW8yD0wuZslmAJRtOr</p>
        </div>

        <div class="info">
            <h3>📋 What This Means:</h3>
            <p>Fast2SMS requires you to verify your website before you can use their OTP Message API. This is a security measure to prevent spam and ensure legitimate usage.</p>
        </div>

        <h2>🚀 Solution Steps:</h2>

        <div class="step">
            <h3>Step 1: Login to Fast2SMS Dashboard</h3>
            <ol>
                <li>Go to <a href="https://www.fast2sms.com/login" target="_blank" class="btn btn-success">Fast2SMS Login</a></li>
                <li>Login with your account credentials</li>
                <li>Navigate to your dashboard</li>
            </ol>
        </div>

        <div class="step">
            <h3>Step 2: Access Website Verification</h3>
            <ol>
                <li>In your Fast2SMS dashboard, look for <strong>"OTP Message"</strong> in the menu</li>
                <li>Click on <strong>"Website Verification"</strong> or <strong>"Verify Website"</strong></li>
                <li>You should see a form to submit your website details</li>
            </ol>
        </div>

        <div class="step">
            <h3>Step 3: Submit Website Details</h3>
            <p>Fill in the following information:</p>
            <ul>
                <li><strong>Website URL:</strong> Your matrimony site URL (e.g., yourdomain.com)</li>
                <li><strong>Website Type:</strong> Matrimony/Dating Website</li>
                <li><strong>Business Category:</strong> Matrimony Services</li>
                <li><strong>Contact Information:</strong> Your business contact details</li>
                <li><strong>Documents:</strong> Upload any required business documents</li>
            </ul>
        </div>

        <div class="step">
            <h3>Step 4: Wait for Approval</h3>
            <ul>
                <li><strong>Processing Time:</strong> Usually 24-48 hours</li>
                <li><strong>Notification:</strong> You'll receive email notification when approved</li>
                <li><strong>Status Check:</strong> Check your Fast2SMS dashboard for status updates</li>
            </ul>
        </div>

        <div class="warning">
            <h3>⚠️ If Website Verification Takes Too Long:</h3>
            <p>You can use alternative solutions while waiting for verification:</p>
            <ol>
                <li><strong>Use DLT Route:</strong> Register a sender ID and use DLT route</li>
                <li><strong>Switch to MSG91:</strong> Alternative SMS provider</li>
                <li><strong>Use Textlocal:</strong> Another reliable SMS service</li>
            </ol>
        </div>

        <h2>🔄 Alternative Solutions:</h2>

        <div class="step">
            <h3>Option A: Use Fast2SMS DLT Route</h3>
            <ol>
                <li>In Fast2SMS dashboard, go to <strong>"DLT"</strong> section</li>
                <li>Register a 6-character sender ID (e.g., "MATRIM", "WEDLOK")</li>
                <li>Wait for sender ID approval (usually 24-48 hours)</li>
                <li>Update your SMS settings to use DLT route with approved sender ID</li>
            </ol>
            <div class="info">
                <p><strong>DLT Route URL Format:</strong></p>
                <pre>https://www.fast2sms.com/dev/bulkV2?authorization=YOUR_API_KEY&route=dlt&sender_id=YOUR_SENDER_ID&message=Your%20OTP%20is%20{OTP}.%20Do%20not%20share%20with%20anyone.&variables_values={OTP}&flash=0&numbers={MOBILE}</pre>
            </div>
        </div>

        <div class="step">
            <h3>Option B: Switch to MSG91 (Immediate Solution)</h3>
            <ol>
                <li>Sign up at <a href="https://msg91.com" target="_blank" class="btn btn-success">MSG91.com</a></li>
                <li>Get your API key from dashboard</li>
                <li>Register a sender ID</li>
                <li>Update your SMS configuration</li>
            </ol>
            <div class="info">
                <p><strong>MSG91 API Format:</strong></p>
                <pre>https://control.msg91.com/api/sendhttp.php?authkey=YOUR_AUTH_KEY&mobiles={MOBILE}&message=Your%20OTP%20is%20{OTP}.%20Do%20not%20share%20with%20anyone.&sender=YOUR_SENDER_ID&route=4&country=91</pre>
            </div>
        </div>

        <div class="step">
            <h3>Option C: Use Textlocal (Quick Setup)</h3>
            <ol>
                <li>Sign up at <a href="https://www.textlocal.in" target="_blank" class="btn btn-success">Textlocal.in</a></li>
                <li>Get your API key</li>
                <li>Register a sender ID</li>
                <li>Start sending SMS immediately</li>
            </ol>
            <div class="info">
                <p><strong>Textlocal API Format:</strong></p>
                <pre>https://api.textlocal.in/send/?apikey=YOUR_API_KEY&numbers={MOBILE}&message=Your%20OTP%20is%20{OTP}.%20Do%20not%20share%20with%20anyone.&sender=YOUR_SENDER_ID</pre>
            </div>
        </div>

        <h2>🛠️ Update Your SMS Configuration:</h2>

        <div class="info">
            <p>Once you have working SMS credentials, update your configuration in:</p>
            <ul>
                <li><strong>Admin Panel:</strong> <a href="premium_admin/SiteSMSSettings.php" class="btn">SMS Settings</a></li>
                <li><strong>Or manually update:</strong> <code>mobile-apis.php</code> file</li>
            </ul>
        </div>

        <h2>📞 Fast2SMS Support:</h2>

        <div class="info">
            <p>If you need help with Fast2SMS verification:</p>
            <ul>
                <li><strong>Support Email:</strong> <EMAIL></li>
                <li><strong>Support Phone:</strong> Check their website for current number</li>
                <li><strong>Live Chat:</strong> Available on their website</li>
                <li><strong>Help Desk:</strong> Submit ticket through their dashboard</li>
            </ul>
        </div>

        <h2>🧪 Test Your SMS After Setup:</h2>

        <div class="step">
            <p>After completing verification or switching providers:</p>
            <ol>
                <li>Update your SMS configuration</li>
                <li>Test using: <a href="test_fast2sms_otp_route.php" class="btn">SMS Test Tool</a></li>
                <li>Verify OTP delivery on your mobile</li>
                <li>Check admin panel SMS logs</li>
            </ol>
        </div>

        <div class="success">
            <h3>✅ Expected Result After Fix:</h3>
            <p>Once verification is complete or you switch providers, your SMS API should return:</p>
            <pre>{"return":true,"request_id":"*********","message":"SMS sent successfully"}</pre>
        </div>

        <div class="warning">
            <h3>💡 Pro Tips:</h3>
            <ul>
                <li><strong>Keep API keys secure:</strong> Don't share them publicly</li>
                <li><strong>Monitor SMS usage:</strong> Check your account balance regularly</li>
                <li><strong>Test regularly:</strong> Ensure SMS delivery is working</li>
                <li><strong>Have backup provider:</strong> Always have an alternative SMS service ready</li>
            </ul>
        </div>

        <div class="text-center" style="margin-top: 30px;">
            <a href="premium_admin/SiteSMSSettings.php" class="btn btn-success">Configure SMS Settings</a>
            <a href="test_fast2sms_otp_route.php" class="btn btn-warning">Test SMS API</a>
            <a href="https://www.fast2sms.com/login" target="_blank" class="btn btn-danger">Fast2SMS Dashboard</a>
        </div>
    </div>
</body>
</html>
