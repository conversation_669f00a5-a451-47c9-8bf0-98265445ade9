<?php
/**
 * PhonePe Payment Callback Handler
 * Handles the response from PhonePe after payment
 */

include_once 'databaseConn.php';
include_once 'lib/requestHandler.php';
include_once 'phonepe_integration.php';
include_once './class/Config.class.php';

$configObj = new Config();
$DatabaseCo = new DatabaseConn();

// Get PhonePe configuration
$phonepe_config = PhonePeConfig::getConfig($DatabaseCo->dbLink);

if (!$phonepe_config) {
    die('PhonePe configuration not found');
}

// Initialize PhonePe
$phonepe = new PhonePePayment(
    $phonepe_config->phonepe_merchant_id,
    $phonepe_config->phonepe_salt_key,
    $phonepe_config->phonepe_salt_index,
    $phonepe_config->phonepe_is_production == 'YES'
);

$payment_status = 'FAILED';
$transaction_id = '';
$error_message = '';
$success_message = '';

try {
    // Get transaction ID from session or POST data
    if (isset($_SESSION['phonepe_transaction_id'])) {
        $transaction_id = $_SESSION['phonepe_transaction_id'];
    } elseif (isset($_POST['transactionId'])) {
        $transaction_id = $_POST['transactionId'];
    } else {
        throw new Exception('Transaction ID not found');
    }
    
    // Verify payment status with PhonePe
    $verification_response = $phonepe->verifyPayment($transaction_id);
    
    PhonePeUtils::logTransaction($transaction_id, [
        'action' => 'payment_verification',
        'response' => $verification_response
    ]);
    
    if ($verification_response['success'] && isset($verification_response['data']['data'])) {
        $payment_data = $verification_response['data']['data'];
        $payment_status = $payment_data['state'] ?? 'UNKNOWN';
        
        if ($payment_status === 'COMPLETED' && $payment_data['responseCode'] === 'SUCCESS') {
            // Payment successful - update database
            $user_id = $_SESSION['user_id'] ?? '';
            $plan_id = $_SESSION['phonepe_plan_id'] ?? '';
            $amount = $_SESSION['phonepe_amount'] ?? 0;
            
            if ($user_id && $plan_id) {
                // Get user and plan details
                $user_query = $DatabaseCo->dbLink->query("SELECT * FROM register WHERE matri_id='$user_id'");
                $user_data = mysqli_fetch_object($user_query);
                
                $plan_query = $DatabaseCo->dbLink->query("SELECT * FROM membership_plan WHERE plan_id='$plan_id'");
                $plan_data = mysqli_fetch_object($plan_query);
                
                if ($user_data && $plan_data) {
                    // Calculate expiry date
                    $today = date("Y-m-d");
                    $exp_date = date("Y-m-d", strtotime($today . " + " . $plan_data->plan_duration . " days"));
                    
                    // Insert payment record
                    $pmatri_id = $user_data->matri_id;
                    $pname = $user_data->username;
                    $pemail = $user_data->email;
                    $paddress = $user_data->address;
                    $paymode = 'PhonePe Online Payment';
                    $pactive_dt = $today;
                    $p_plan = $plan_data->plan_name;
                    $plan_duration = $plan_data->plan_duration;
                    $profile = $plan_data->profile;
                    $chat = $plan_data->chat;
                    $p_no_contacts = $plan_data->plan_contacts;
                    $p_amount = $amount;
                    $p_bank_detail = 'PhonePe Transaction ID: ' . $transaction_id;
                    $pay_id = $transaction_id;
                    $p_msg = $plan_data->plan_msg;
                    
                    $sql = "INSERT INTO payments (
                        pmatri_id, pname, pemail, paddress, paymode, pactive_dt, 
                        p_plan, plan_duration, profile, chat, p_no_contacts, 
                        p_amount, p_bank_detail, pay_id, p_msg, exp_date
                    ) VALUES (
                        '$pmatri_id', '$pname', '$pemail', '$paddress', '$paymode', '$pactive_dt',
                        '$p_plan', '$plan_duration', '$profile', '$chat', '$p_no_contacts',
                        '$p_amount', '$p_bank_detail', '$pay_id', '$p_msg', '$exp_date'
                    )";
                    
                    if ($DatabaseCo->dbLink->query($sql)) {
                        // Update user status
                        $DatabaseCo->dbLink->query("UPDATE register SET status='Paid' WHERE matri_id='$user_id'");
                        
                        $success_message = 'Payment successful! Your membership has been activated.';
                        $payment_status = 'SUCCESS';
                        
                        // Clear session data
                        unset($_SESSION['phonepe_transaction_id']);
                        unset($_SESSION['phonepe_plan_id']);
                        unset($_SESSION['phonepe_amount']);
                        
                        PhonePeUtils::logTransaction($transaction_id, [
                            'action' => 'payment_success',
                            'user_id' => $user_id,
                            'plan_id' => $plan_id,
                            'amount' => $amount
                        ]);
                    } else {
                        throw new Exception('Failed to update payment record in database');
                    }
                } else {
                    throw new Exception('User or plan data not found');
                }
            } else {
                throw new Exception('Session data missing');
            }
        } else {
            $error_message = PhonePeUtils::getStatusMessage($payment_data['responseCode'] ?? 'UNKNOWN');
            $payment_status = 'FAILED';
        }
    } else {
        throw new Exception('Failed to verify payment status');
    }
    
} catch (Exception $e) {
    $error_message = $e->getMessage();
    $payment_status = 'ERROR';
    
    PhonePeUtils::logTransaction($transaction_id, [
        'action' => 'payment_error',
        'error' => $error_message
    ], 'ERROR');
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Payment Status - <?php echo $configObj->getConfigTitle(); ?></title>
    
    <!-- CSS Files -->
    <link href="css/bootstrap.css" rel="stylesheet">
    <link href="css/custom.css" rel="stylesheet">
    <link href="css/icon-fixes.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/48403ccd1a.js" crossorigin="anonymous"></script>
    
    <style>
        .status-container {
            max-width: 600px;
            margin: 50px auto;
            padding: 40px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            text-align: center;
        }
        .status-icon {
            font-size: 80px;
            margin-bottom: 20px;
        }
        .status-success {
            color: #28a745;
        }
        .status-failed {
            color: #dc3545;
        }
        .status-pending {
            color: #ffc107;
        }
        .status-title {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 15px;
        }
        .status-message {
            font-size: 16px;
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        .transaction-details {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: left;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .detail-row:last-child {
            border-bottom: none;
        }
        .action-buttons {
            margin-top: 30px;
        }
        .btn-custom {
            padding: 12px 30px;
            border-radius: 8px;
            font-weight: 600;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-custom:hover {
            transform: translateY(-2px);
            text-decoration: none;
            color: white;
        }
        @media (max-width: 768px) {
            .status-container {
                margin: 20px;
                padding: 30px 20px;
            }
            .status-icon {
                font-size: 60px;
            }
            .status-title {
                font-size: 24px;
            }
            .detail-row {
                flex-direction: column;
                gap: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="status-container">
            <?php if ($payment_status === 'SUCCESS'): ?>
                <!-- Success Status -->
                <div class="status-icon status-success">
                    <i class="fa fa-check-circle"></i>
                </div>
                <h2 class="status-title status-success">Payment Successful!</h2>
                <p class="status-message">
                    <?php echo htmlspecialchars($success_message); ?>
                    Your membership has been activated and you can now enjoy all the premium features.
                </p>
                
                <div class="transaction-details">
                    <h4><i class="fa fa-receipt"></i> Transaction Details</h4>
                    <div class="detail-row">
                        <span>Transaction ID:</span>
                        <span><?php echo htmlspecialchars($transaction_id); ?></span>
                    </div>
                    <div class="detail-row">
                        <span>Payment Method:</span>
                        <span>PhonePe</span>
                    </div>
                    <div class="detail-row">
                        <span>Status:</span>
                        <span class="status-success">Completed</span>
                    </div>
                    <div class="detail-row">
                        <span>Date:</span>
                        <span><?php echo date('d M Y, h:i A'); ?></span>
                    </div>
                </div>
                
                <div class="action-buttons">
                    <a href="dashboard.php" class="btn-custom btn-success">
                        <i class="fa fa-tachometer-alt"></i> Go to Dashboard
                    </a>
                    <a href="membershipplans.php" class="btn-custom btn-primary">
                        <i class="fa fa-crown"></i> View Plans
                    </a>
                </div>
                
            <?php elseif ($payment_status === 'PENDING'): ?>
                <!-- Pending Status -->
                <div class="status-icon status-pending">
                    <i class="fa fa-clock"></i>
                </div>
                <h2 class="status-title status-pending">Payment Pending</h2>
                <p class="status-message">
                    Your payment is being processed. Please wait for confirmation.
                    You will receive an email once the payment is completed.
                </p>
                
                <div class="transaction-details">
                    <h4><i class="fa fa-info-circle"></i> Transaction Details</h4>
                    <div class="detail-row">
                        <span>Transaction ID:</span>
                        <span><?php echo htmlspecialchars($transaction_id); ?></span>
                    </div>
                    <div class="detail-row">
                        <span>Status:</span>
                        <span class="status-pending">Pending</span>
                    </div>
                </div>
                
                <div class="action-buttons">
                    <a href="dashboard.php" class="btn-custom btn-primary">
                        <i class="fa fa-home"></i> Go to Dashboard
                    </a>
                    <a href="javascript:location.reload()" class="btn-custom btn-secondary">
                        <i class="fa fa-refresh"></i> Check Status
                    </a>
                </div>
                
            <?php else: ?>
                <!-- Failed Status -->
                <div class="status-icon status-failed">
                    <i class="fa fa-times-circle"></i>
                </div>
                <h2 class="status-title status-failed">Payment Failed</h2>
                <p class="status-message">
                    <?php echo htmlspecialchars($error_message ?: 'Your payment could not be processed. Please try again.'); ?>
                </p>
                
                <div class="transaction-details">
                    <h4><i class="fa fa-exclamation-triangle"></i> Transaction Details</h4>
                    <div class="detail-row">
                        <span>Transaction ID:</span>
                        <span><?php echo htmlspecialchars($transaction_id ?: 'N/A'); ?></span>
                    </div>
                    <div class="detail-row">
                        <span>Status:</span>
                        <span class="status-failed">Failed</span>
                    </div>
                    <div class="detail-row">
                        <span>Date:</span>
                        <span><?php echo date('d M Y, h:i A'); ?></span>
                    </div>
                </div>
                
                <div class="action-buttons">
                    <a href="paymentOptions.php?pid=<?php echo $_SESSION['phonepe_plan_id'] ?? ''; ?>" class="btn-custom btn-primary">
                        <i class="fa fa-retry"></i> Try Again
                    </a>
                    <a href="membershipplans.php" class="btn-custom btn-secondary">
                        <i class="fa fa-arrow-left"></i> Back to Plans
                    </a>
                </div>
            <?php endif; ?>
            
            <!-- Support Info -->
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef; font-size: 14px; color: #666;">
                <p>
                    <i class="fa fa-headset"></i>
                    Need help? Contact our support team at 
                    <a href="mailto:support@<?php echo $_SERVER['HTTP_HOST']; ?>">support@<?php echo $_SERVER['HTTP_HOST']; ?></a>
                </p>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/jquery.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    
    <?php if ($payment_status === 'SUCCESS'): ?>
    <script>
        // Auto-redirect to dashboard after 10 seconds for successful payments
        setTimeout(function() {
            window.location.href = 'dashboard.php';
        }, 10000);
        
        // Show countdown
        var countdown = 10;
        var countdownElement = $('<p style="margin-top: 20px; color: #666; font-size: 14px;">Redirecting to dashboard in <span id="countdown">10</span> seconds...</p>');
        $('.action-buttons').after(countdownElement);
        
        var timer = setInterval(function() {
            countdown--;
            $('#countdown').text(countdown);
            if (countdown <= 0) {
                clearInterval(timer);
            }
        }, 1000);
    </script>
    <?php endif; ?>
</body>
</html>
