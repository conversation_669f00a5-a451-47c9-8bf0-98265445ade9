<?php
/**
 * SMS & Theme Dashboard Widget
 * Include this in the admin dashboard to show quick status and links
 */

include_once '../databaseConn.php';
$DatabaseCo = new DatabaseConn();

// Check if tables exist
$sms_table_exists = false;
$theme_table_exists = false;

$tables_check = $DatabaseCo->dbLink->query("SHOW TABLES LIKE 'sms_settings'");
if (mysqli_num_rows($tables_check) > 0) {
    $sms_table_exists = true;
}

$tables_check = $DatabaseCo->dbLink->query("SHOW TABLES LIKE 'theme_settings'");
if (mysqli_num_rows($tables_check) > 0) {
    $theme_table_exists = true;
}

// Get current settings
$sms_provider = 'Not Configured';
$theme_colors = 'Default';

if ($sms_table_exists) {
    $sms_sql = $DatabaseCo->dbLink->query("SELECT provider FROM sms_settings WHERE id='1'");
    if ($sms_sql && mysqli_num_rows($sms_sql) > 0) {
        $sms_data = mysqli_fetch_object($sms_sql);
        $sms_provider = ucfirst($sms_data->provider);
    }
}

if ($theme_table_exists) {
    $theme_sql = $DatabaseCo->dbLink->query("SELECT primary_color, secondary_color FROM theme_settings WHERE id='1'");
    if ($theme_sql && mysqli_num_rows($theme_sql) > 0) {
        $theme_data = mysqli_fetch_object($theme_sql);
        $theme_colors = "Custom ({$theme_data->primary_color}, {$theme_data->secondary_color})";
    }
}
?>

<div class="row">
    <!-- SMS Settings Widget -->
    <div class="col-md-6">
        <div class="box box-primary">
            <div class="box-header with-border">
                <h3 class="box-title"><i class="fa fa-sms"></i> SMS Gateway Status</h3>
                <div class="box-tools pull-right">
                    <button type="button" class="btn btn-box-tool" data-widget="collapse">
                        <i class="fa fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="box-body">
                <div class="info-box">
                    <span class="info-box-icon bg-blue"><i class="fa fa-mobile"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Current Provider</span>
                        <span class="info-box-number"><?php echo $sms_provider; ?></span>
                    </div>
                </div>
                
                <?php if (!$sms_table_exists): ?>
                    <div class="alert alert-warning">
                        <i class="fa fa-exclamation-triangle"></i> SMS settings not initialized.
                        <a href="setup_sms_theme.php" class="btn btn-sm btn-warning">Run Setup</a>
                    </div>
                <?php else: ?>
                    <div class="btn-group btn-group-justified">
                        <a href="SiteSMSSettings.php" class="btn btn-primary">
                            <i class="fa fa-cog"></i> Configure SMS
                        </a>
                        <a href="SiteSMSSettings.php#test" class="btn btn-success">
                            <i class="fa fa-paper-plane"></i> Test SMS
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Theme Settings Widget -->
    <div class="col-md-6">
        <div class="box box-success">
            <div class="box-header with-border">
                <h3 class="box-title"><i class="fa fa-palette"></i> Theme Customization</h3>
                <div class="box-tools pull-right">
                    <button type="button" class="btn btn-box-tool" data-widget="collapse">
                        <i class="fa fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="box-body">
                <div class="info-box">
                    <span class="info-box-icon bg-green"><i class="fa fa-paint-brush"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Color Scheme</span>
                        <span class="info-box-number" style="font-size: 14px;"><?php echo $theme_colors; ?></span>
                    </div>
                </div>
                
                <?php if (!$theme_table_exists): ?>
                    <div class="alert alert-warning">
                        <i class="fa fa-exclamation-triangle"></i> Theme settings not initialized.
                        <a href="setup_sms_theme.php" class="btn btn-sm btn-warning">Run Setup</a>
                    </div>
                <?php else: ?>
                    <div class="btn-group btn-group-justified">
                        <a href="SiteThemeSettings.php" class="btn btn-success">
                            <i class="fa fa-palette"></i> Customize Colors
                        </a>
                        <a href="../" target="_blank" class="btn btn-info">
                            <i class="fa fa-eye"></i> Preview Site
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions Row -->
<div class="row">
    <div class="col-md-12">
        <div class="box box-warning">
            <div class="box-header with-border">
                <h3 class="box-title"><i class="fa fa-bolt"></i> Quick Actions</h3>
            </div>
            <div class="box-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="SiteSMSSettings.php" class="btn btn-block btn-primary">
                            <i class="fa fa-sms"></i><br>
                            SMS Settings
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="SiteThemeSettings.php" class="btn btn-block btn-success">
                            <i class="fa fa-palette"></i><br>
                            Theme Colors
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="../diagnose_otp_issue.php" target="_blank" class="btn btn-block btn-info">
                            <i class="fa fa-stethoscope"></i><br>
                            Diagnose OTP
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="setup_sms_theme.php" class="btn btn-block btn-warning">
                            <i class="fa fa-cog"></i><br>
                            Run Setup
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.btn i {
    margin-bottom: 5px;
    display: block;
    font-size: 18px;
}
.info-box-number {
    font-size: 16px !important;
}
</style>
