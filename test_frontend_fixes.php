<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>Frontend Fixes Test - Matrimony Site</title>
    
    <!-- Bootstrap CSS -->
    <link href="css/bootstrap.css" rel="stylesheet">
    <link href="css/custom-responsive.css" rel="stylesheet">
    <link href="css/custom.css" rel="stylesheet">
    
    <!-- Icon and Mobile Responsive Fixes -->
    <link href="css/icon-fixes.css" rel="stylesheet">
    <link href="css/mobile-responsive-fixes.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/48403ccd1a.js" crossorigin="anonymous"></script>
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    
    <style>
        body { padding: 20px; background: #f5f5f5; }
        .test-section { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .icon-test { font-size: 24px; margin: 10px; display: inline-block; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center">🔧 Frontend Fixes Test Page</h1>
        <p class="text-center text-muted">Testing icon fixes and mobile responsiveness</p>
        
        <!-- Icon Tests -->
        <div class="test-section">
            <h2><i class="fa fa-star"></i> Icon Tests</h2>
            
            <h3>Font Awesome Icons</h3>
            <div class="row">
                <div class="col-md-3 col-sm-6">
                    <i class="fa fa-user icon-test"></i> fa-user
                </div>
                <div class="col-md-3 col-sm-6">
                    <i class="fa fa-envelope icon-test"></i> fa-envelope
                </div>
                <div class="col-md-3 col-sm-6">
                    <i class="fa fa-phone icon-test"></i> fa-phone
                </div>
                <div class="col-md-3 col-sm-6">
                    <i class="fa fa-heart icon-test"></i> fa-heart
                </div>
            </div>
            
            <h3>Bootstrap Icons (Glyphicon Replacements)</h3>
            <div class="row">
                <div class="col-md-3 col-sm-6">
                    <i class="gi gi-user icon-test"></i> gi-user
                </div>
                <div class="col-md-3 col-sm-6">
                    <i class="gi gi-envelope icon-test"></i> gi-envelope
                </div>
                <div class="col-md-3 col-sm-6">
                    <i class="gi gi-phone icon-test"></i> gi-phone
                </div>
                <div class="col-md-3 col-sm-6">
                    <i class="gi gi-heart icon-test"></i> gi-heart
                </div>
            </div>
            
            <h3>Animated Icons</h3>
            <div class="row">
                <div class="col-md-3 col-sm-6">
                    <i class="gi gi-loader gi-spin icon-test"></i> Loading
                </div>
                <div class="col-md-3 col-sm-6">
                    <i class="fa fa-heart icon-pulse icon-test"></i> Pulse
                </div>
                <div class="col-md-3 col-sm-6">
                    <i class="fa fa-star icon-bounce icon-test"></i> Bounce
                </div>
                <div class="col-md-3 col-sm-6">
                    <i class="fa fa-refresh fa-spin icon-test"></i> Spin
                </div>
            </div>
        </div>
        
        <!-- Form Tests -->
        <div class="test-section">
            <h2><i class="fa fa-edit"></i> Form Elements Test</h2>
            
            <div class="form-group">
                <label class="gt-text-light-Grey">
                    <b class="text-danger mr-5 gtRegMandatory">*</b><b>Test Input Field</b>
                </label>
                <input type="text" class="gt-form-control" placeholder="Test input field">
            </div>
            
            <div class="form-group">
                <label class="gt-text-light-Grey">
                    <b class="text-danger mr-5 gtRegMandatory">*</b><b>Test Select Field</b>
                </label>
                <select class="gt-form-control">
                    <option>Select an option</option>
                    <option>Option 1</option>
                    <option>Option 2</option>
                    <option>Option 3</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="gt-text-light-Grey">
                    <b>Radio Buttons</b>
                </label>
                <div class="mt-10">
                    <input type="radio" name="test_radio" value="option1" id="option1" class="mt-0 pull-left pr-10">
                    <label class="pull-left font-13 gt-font-weight-500 pl-5 pr-10" for="option1">Option 1</label>
                    
                    <input type="radio" name="test_radio" value="option2" id="option2" class="mt-0 pull-left pr-10">
                    <label class="pull-left font-13 gt-font-weight-500 pl-5 pr-10" for="option2">Option 2</label>
                </div>
            </div>
            
            <div class="form-group">
                <button type="button" class="btn gt-btn-green">
                    <i class="fa fa-check"></i> Primary Button
                </button>
                <button type="button" class="btn gt-btn-orange">
                    <i class="fa fa-heart"></i> Secondary Button
                </button>
            </div>
        </div>
        
        <!-- Registration Title Tests -->
        <div class="test-section">
            <h2><i class="fa fa-user-circle"></i> Registration Titles Test</h2>
            
            <h4 class="gtRegTitle mt-30 inThemeOrange">
                <i class="fas fa-user-circle mr-10 fa-fw"></i> Account Information
            </h4>
            
            <h4 class="gtRegTitle mt-30 inThemeOrange">
                <i class="fa fa-user mr-10 fa-fw"></i> Personal Information
            </h4>
            
            <h4 class="gtRegTitle mt-30 inThemeOrange">
                <i class="fas fa-walking mr-10 fa-fw"></i> Physical Attributes
            </h4>
        </div>
        
        <!-- Mobile Responsiveness Test -->
        <div class="test-section">
            <h2><i class="fa fa-mobile"></i> Mobile Responsiveness Test</h2>
            
            <div class="alert alert-info">
                <strong>Test Instructions:</strong>
                <ul>
                    <li>Resize your browser window to test mobile view</li>
                    <li>Use browser developer tools to simulate mobile devices</li>
                    <li>Check if forms are touch-friendly (minimum 44px touch targets)</li>
                    <li>Verify icons are visible and properly sized</li>
                </ul>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="gt-panel">
                        <div class="gt-panel-head">
                            <i class="fa fa-info-circle"></i> Panel Test
                        </div>
                        <div class="gt-panel-body">
                            This panel should be responsive and look good on mobile devices.
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="gt-panel">
                        <div class="gt-panel-head">
                            <i class="fa fa-cog"></i> Another Panel
                        </div>
                        <div class="gt-panel-body">
                            Icons should be visible and properly aligned.
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Status Check -->
        <div class="test-section">
            <h2><i class="fa fa-check-circle"></i> Fix Status</h2>
            
            <div class="row">
                <div class="col-md-6">
                    <h4 class="success">✅ Fixed Issues:</h4>
                    <ul>
                        <li>Missing Glyphicon icons replaced with Bootstrap Icons</li>
                        <li>Font Awesome 6 compatibility issues resolved</li>
                        <li>Mobile viewport optimized</li>
                        <li>Touch-friendly form elements</li>
                        <li>Responsive button sizing</li>
                        <li>Icon animations working</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h4 class="info">📱 Mobile Improvements:</h4>
                    <ul>
                        <li>Proper viewport meta tag</li>
                        <li>Touch-friendly minimum sizes (44px)</li>
                        <li>Responsive form layouts</li>
                        <li>Mobile-optimized typography</li>
                        <li>Improved accessibility</li>
                        <li>Better contrast ratios</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Navigation Links -->
        <div class="test-section text-center">
            <h2><i class="fa fa-link"></i> Test Navigation</h2>
            <a href="register.php" class="btn gt-btn-green">
                <i class="fa fa-user-plus"></i> Test Registration Form
            </a>
            <a href="login.php" class="btn gt-btn-orange">
                <i class="fa fa-sign-in-alt"></i> Test Login Form
            </a>
            <a href="index.php" class="btn btn-info">
                <i class="fa fa-home"></i> Back to Homepage
            </a>
        </div>
    </div>
    
    <!-- JavaScript for testing -->
    <script src="js/jquery.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script>
        $(document).ready(function() {
            // Test if icons are loading
            setTimeout(function() {
                var iconTest = $('.fa').length + $('.gi').length;
                if (iconTest > 0) {
                    console.log('✅ Icons loaded successfully: ' + iconTest + ' icons found');
                } else {
                    console.log('❌ Icons not loading properly');
                }
            }, 1000);
            
            // Test responsive behavior
            function checkViewport() {
                var width = $(window).width();
                var status = '';
                if (width < 768) {
                    status = 'Mobile view (< 768px)';
                } else if (width < 992) {
                    status = 'Tablet view (768px - 991px)';
                } else {
                    status = 'Desktop view (≥ 992px)';
                }
                console.log('Current viewport: ' + width + 'px - ' + status);
            }
            
            checkViewport();
            $(window).resize(checkViewport);
        });
    </script>
</body>
</html>
