# 🚀 SMS Gateway & Theme Customization Setup Guide

## 📋 **What's Been Created**

### **1. SMS Gateway Management**
- **Admin Page**: `premium_admin/SiteSMSSettings.php`
- **Database Table**: `sms_settings`
- **Supported Providers**: Fast2SMS, MSG91, Textlocal
- **Features**: 
  - Provider selection
  - API key management
  - Test SMS functionality
  - Auto-update mobile-apis.php

### **2. Theme & Color Customization**
- **Admin Page**: `premium_admin/SiteThemeSettings.php`
- **Database Table**: `theme_settings`
- **Features**:
  - Primary, secondary, accent colors
  - Button and link colors
  - Header/footer background colors
  - Real-time color preview
  - Reset to default option

### **3. Enhanced SMS Handler**
- **File**: `sms_handler.php` (updated)
- **Features**:
  - Database-driven configuration
  - Multiple provider support
  - Automatic fallback
  - Comprehensive error logging

## 🛠️ **Installation Steps**

### **Step 1: Run Setup Script**
1. Navigate to: `your-domain.com/premium_admin/setup_sms_theme.php`
2. This will:
   - Create required database tables
   - Insert default settings
   - Update mobile-apis.php
   - Create initial theme CSS

### **Step 2: Configure SMS Gateway**
1. Go to **Admin Panel → Site Settings → SMS Gateway Settings**
2. Choose your SMS provider:
   - **Fast2SMS**: Enter API key, sender ID, select route
   - **MSG91**: Enter API key and sender ID
   - **Textlocal**: Enter API key and sender ID
3. Test SMS functionality with a real mobile number

### **Step 3: Customize Theme Colors**
1. Go to **Admin Panel → Site Settings → Theme & Color Settings**
2. Customize colors:
   - Primary colors (green, orange, blue themes)
   - Status colors (success, warning, danger)
   - Button and link colors
   - Layout colors (header, footer)
3. Save settings to apply changes

### **Step 4: Include Theme CSS in Templates**
Add this line to your site templates (in header section):
```php
<?php include 'parts/theme_css.php'; ?>
```

## 📱 **SMS Provider Setup Guides**

### **Fast2SMS Setup**
1. Sign up at [Fast2SMS](https://www.fast2sms.com)
2. Complete website verification for OTP route
3. Get API key from dashboard
4. For DLT route: Register and get approved sender ID

### **MSG91 Setup**
1. Sign up at [MSG91](https://msg91.com)
2. Get API key from dashboard
3. Register sender ID
4. Complete DLT registration if required

### **Textlocal Setup**
1. Sign up at [Textlocal](https://www.textlocal.in)
2. Get API key from dashboard
3. Register sender ID
4. Add credits to account

## 🎨 **Theme Customization Features**

### **Color Categories**
- **Primary Colors**: Main theme colors (green, orange, blue)
- **Status Colors**: Success, warning, danger, dark, light
- **Interactive Colors**: Buttons, links, hover states
- **Layout Colors**: Header, footer, sidebar backgrounds

### **CSS Variables**
The system generates CSS custom properties:
```css
:root {
  --primary-color: #499202;
  --secondary-color: #e47203;
  --accent-color: #089cbe;
  /* ... more variables */
}
```

## 🔧 **Technical Details**

### **Database Tables**

#### `sms_settings` Table
```sql
- id (Primary Key)
- provider (fast2sms/msg91/textlocal)
- fast2sms_api_key
- fast2sms_sender_id
- fast2sms_route (otp/dlt)
- msg91_api_key
- msg91_sender_id
- textlocal_api_key
- textlocal_sender_id
- is_active
- test_mobile
- created_at, updated_at
```

#### `theme_settings` Table
```sql
- id (Primary Key)
- primary_color, secondary_color, accent_color
- success_color, warning_color, danger_color
- dark_color, light_color
- button_primary_color, button_secondary_color
- link_color, link_hover_color
- header_bg_color, footer_bg_color, sidebar_bg_color
- is_active
- created_at, updated_at
```

### **File Structure**
```
premium_admin/
├── SiteSMSSettings.php          # SMS gateway admin page
├── SiteThemeSettings.php        # Theme customization admin page
├── setup_sms_theme.php          # One-time setup script
├── sms_theme_dashboard_widget.php # Dashboard widget
└── page-part/left_panel.php     # Updated with new menu items

root/
├── sms_handler.php              # Enhanced SMS handler
├── mobile-apis.php              # Auto-updated SMS API config
├── parts/theme_css.php          # Dynamic theme CSS include
├── css/theme-custom.css         # Generated theme CSS
└── diagnose_otp_issue.php       # OTP diagnostic tool
```

## 🧪 **Testing**

### **SMS Testing**
1. Use the test feature in SMS settings
2. Run diagnostic script: `your-domain.com/diagnose_otp_issue.php`
3. Check error logs in `sms_error_log.txt`

### **Theme Testing**
1. Change colors in theme settings
2. Preview changes on frontend
3. Test different color combinations
4. Use browser developer tools to verify CSS

## 🚨 **Troubleshooting**

### **SMS Issues**
- **Fast2SMS Error 996**: Complete website verification
- **Fast2SMS Error 406**: Invalid sender ID - get approved sender ID
- **No SMS received**: Check mobile number format, API credits
- **API errors**: Verify API keys and provider settings

### **Theme Issues**
- **Colors not applying**: Ensure `parts/theme_css.php` is included
- **CSS conflicts**: Check for CSS specificity issues
- **Database errors**: Run setup script again

## 📞 **Support**

### **Quick Fixes**
1. **Re-run setup**: `premium_admin/setup_sms_theme.php`
2. **Reset theme**: Use "Reset to Default" in theme settings
3. **Check logs**: Look at `sms_error_log.txt` for SMS issues
4. **Verify database**: Ensure tables exist and have data

### **Admin Panel Access**
- **SMS Settings**: Admin Panel → Site Settings → SMS Gateway Settings
- **Theme Settings**: Admin Panel → Site Settings → Theme & Color Settings
- **Dashboard Widget**: Shows quick status and links

---

**🎉 Congratulations!** Your matrimony site now has professional SMS gateway management and theme customization capabilities!
