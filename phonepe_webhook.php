<?php
/**
 * PhonePe Webhook Handler
 * Handles server-to-server notifications from PhonePe
 */

include_once 'databaseConn.php';
include_once 'phonepe_integration.php';

// Set content type for JSON response
header('Content-Type: application/json');

$DatabaseCo = new DatabaseConn();

// Get PhonePe configuration
$phonepe_config = PhonePeConfig::getConfig($DatabaseCo->dbLink);

if (!$phonepe_config) {
    http_response_code(500);
    echo json_encode(['status' => 'error', 'message' => 'Configuration not found']);
    exit;
}

try {
    // Get raw POST data
    $raw_payload = file_get_contents('php://input');
    
    if (empty($raw_payload)) {
        throw new Exception('Empty payload received');
    }
    
    // Get headers
    $headers = getallheaders();
    $signature = isset($headers['X-VERIFY']) ? $headers['X-VERIFY'] : '';
    
    if (empty($signature)) {
        throw new Exception('Missing signature header');
    }
    
    // Initialize PhonePe
    $phonepe = new PhonePePayment(
        $phonepe_config->phonepe_merchant_id,
        $phonepe_config->phonepe_salt_key,
        $phonepe_config->phonepe_salt_index,
        $phonepe_config->phonepe_is_production == 'YES'
    );
    
    // Validate webhook signature
    if (!$phonepe->validateWebhookSignature($raw_payload, $signature)) {
        throw new Exception('Invalid signature');
    }
    
    // Decode payload
    $payload = json_decode($raw_payload, true);
    
    if (!$payload) {
        throw new Exception('Invalid JSON payload');
    }
    
    // Log webhook received
    PhonePeUtils::logTransaction($payload['data']['merchantTransactionId'] ?? 'UNKNOWN', [
        'action' => 'webhook_received',
        'payload' => $payload
    ]);
    
    // Process webhook based on event type
    if (isset($payload['data'])) {
        $transaction_data = $payload['data'];
        $transaction_id = $transaction_data['merchantTransactionId'];
        $status = $transaction_data['state'] ?? 'UNKNOWN';
        $response_code = $transaction_data['responseCode'] ?? '';
        
        // Update transaction status in database
        $update_sql = "UPDATE payments SET 
                      p_bank_detail = CONCAT(p_bank_detail, ' | Webhook Status: $status'),
                      updated_at = NOW()
                      WHERE pay_id = '$transaction_id'";
        
        $DatabaseCo->dbLink->query($update_sql);
        
        // Handle different payment states
        switch ($status) {
            case 'COMPLETED':
                if ($response_code === 'SUCCESS') {
                    // Payment successful - additional processing if needed
                    PhonePeUtils::logTransaction($transaction_id, [
                        'action' => 'webhook_success',
                        'status' => $status,
                        'response_code' => $response_code
                    ]);
                    
                    // You can add additional logic here like:
                    // - Send confirmation email
                    // - Update user privileges
                    // - Trigger other services
                    
                } else {
                    // Payment completed but with error
                    PhonePeUtils::logTransaction($transaction_id, [
                        'action' => 'webhook_completed_with_error',
                        'status' => $status,
                        'response_code' => $response_code
                    ], 'WARNING');
                }
                break;
                
            case 'FAILED':
                // Payment failed
                PhonePeUtils::logTransaction($transaction_id, [
                    'action' => 'webhook_failed',
                    'status' => $status,
                    'response_code' => $response_code
                ], 'ERROR');
                
                // Update payment record to reflect failure
                $fail_update_sql = "UPDATE payments SET 
                                   p_bank_detail = CONCAT(p_bank_detail, ' | FAILED: $response_code')
                                   WHERE pay_id = '$transaction_id'";
                $DatabaseCo->dbLink->query($fail_update_sql);
                break;
                
            case 'PENDING':
                // Payment still pending
                PhonePeUtils::logTransaction($transaction_id, [
                    'action' => 'webhook_pending',
                    'status' => $status
                ], 'INFO');
                break;
                
            default:
                // Unknown status
                PhonePeUtils::logTransaction($transaction_id, [
                    'action' => 'webhook_unknown_status',
                    'status' => $status,
                    'payload' => $payload
                ], 'WARNING');
        }
        
        // Send success response to PhonePe
        http_response_code(200);
        echo json_encode([
            'status' => 'success',
            'message' => 'Webhook processed successfully',
            'transactionId' => $transaction_id
        ]);
        
    } else {
        throw new Exception('Invalid payload structure');
    }
    
} catch (Exception $e) {
    // Log error
    PhonePeUtils::logTransaction('WEBHOOK_ERROR', [
        'action' => 'webhook_error',
        'error' => $e->getMessage(),
        'payload' => $raw_payload ?? 'N/A'
    ], 'ERROR');
    
    // Send error response
    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}

/**
 * Get all headers (compatibility function)
 */
if (!function_exists('getallheaders')) {
    function getallheaders() {
        $headers = [];
        foreach ($_SERVER as $name => $value) {
            if (substr($name, 0, 5) == 'HTTP_') {
                $headers[str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($name, 5)))))] = $value;
            }
        }
        return $headers;
    }
}
?>
