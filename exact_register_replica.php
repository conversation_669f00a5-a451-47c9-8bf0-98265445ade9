<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>Register Now - Exact Replica</title>
    
    <!-- CSS Files -->
    <link href="css/bootstrap.css" rel="stylesheet">
    <link href="css/icon-fixes.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/48403ccd1a.js" crossorigin="anonymous"></script>
    
    <style>
        body {
            background: linear-gradient(135deg, #8B5CF6 0%, #A855F7 50%, #C084FC 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px 0;
        }
        
        .register-container {
            max-width: 450px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 40px 35px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .register-title {
            text-align: center;
            color: #FF6B35;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 35px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .form-group-modern {
            position: relative;
            margin-bottom: 20px;
        }
        
        .form-group-modern .icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #FF6B35;
            font-size: 16px;
            z-index: 2;
            width: 18px;
            text-align: center;
        }
        
        .form-group-modern input,
        .form-group-modern select {
            width: 100%;
            padding: 15px 15px 15px 45px;
            border: 2px solid #E5E7EB;
            border-radius: 12px;
            font-size: 15px;
            background: #FAFAFA;
            transition: all 0.3s ease;
            outline: none;
            color: #374151;
        }
        
        .form-group-modern input:focus,
        .form-group-modern select:focus {
            border-color: #FF6B35;
            background: white;
            box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
        }
        
        .form-group-modern input::placeholder {
            color: #9CA3AF;
            font-size: 14px;
        }
        
        .form-group-modern select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%239ca3af' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 12px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
            cursor: pointer;
        }
        
        .date-row {
            display: flex;
            gap: 8px;
            margin-bottom: 20px;
        }
        
        .date-row .form-group-modern {
            flex: 1;
            margin-bottom: 0;
        }
        
        .phone-row {
            display: flex;
            gap: 8px;
            margin-bottom: 20px;
        }
        
        .phone-row .country-code {
            flex: 0 0 90px;
        }
        
        .phone-row .phone-number {
            flex: 1;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            margin: 25px 0;
            font-size: 14px;
            color: #6B7280;
        }
        
        .checkbox-group input[type="checkbox"] {
            margin-right: 12px;
            transform: scale(1.3);
            accent-color: #FF6B35;
        }
        
        .checkbox-group a {
            color: #FF6B35;
            text-decoration: none;
            font-weight: 600;
        }
        
        .register-btn {
            width: 100%;
            background: linear-gradient(135deg, #22C55E 0%, #16A34A 100%);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            padding: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);
        }
        
        .register-btn:active {
            transform: translateY(0);
        }
        
        /* Mobile Responsive */
        @media (max-width: 480px) {
            .register-container {
                margin: 10px;
                padding: 30px 25px;
            }
            
            .date-row {
                flex-direction: column;
                gap: 0;
            }
            
            .date-row .form-group-modern {
                margin-bottom: 20px;
            }
            
            .phone-row {
                flex-direction: column;
                gap: 0;
            }
            
            .phone-row .country-code,
            .phone-row .phone-number {
                flex: none;
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <h1 class="register-title">Register Now</h1>
        
        <form id="registerForm" method="post" action="">
            
            <!-- Profile Created By -->
            <div class="form-group-modern">
                <i class="fa fa-users icon"></i>
                <select name="profile_by" required>
                    <option value="">Profile Created By</option>
                    <option value="Self">Self</option>
                    <option value="Parents">Parents</option>
                    <option value="Guardian">Guardian</option>
                    <option value="Relative">Relative</option>
                    <option value="Friend">Friend</option>
                </select>
            </div>
            
            <!-- Gender -->
            <div class="form-group-modern">
                <i class="fa fa-venus-mars icon"></i>
                <select name="gender" required>
                    <option value="">Select Gender</option>
                    <option value="Male">Male</option>
                    <option value="Female">Female</option>
                </select>
            </div>
            
            <!-- First Name -->
            <div class="form-group-modern">
                <i class="fa fa-user icon"></i>
                <input type="text" name="first_name" placeholder="Enter First Name" required>
            </div>
            
            <!-- Last Name -->
            <div class="form-group-modern">
                <i class="fa fa-user icon"></i>
                <input type="text" name="last_name" placeholder="Enter Last Name" required>
            </div>
            
            <!-- Date of Birth -->
            <div class="date-row">
                <div class="form-group-modern">
                    <i class="fa fa-calendar icon"></i>
                    <select name="birth_day" required>
                        <option value="">01</option>
                        <?php for($i = 1; $i <= 31; $i++): ?>
                            <option value="<?php echo sprintf('%02d', $i); ?>"><?php echo sprintf('%02d', $i); ?></option>
                        <?php endfor; ?>
                    </select>
                </div>
                <div class="form-group-modern">
                    <i class="fa fa-calendar icon"></i>
                    <select name="birth_month" required>
                        <option value="">Month</option>
                        <option value="01">January</option>
                        <option value="02">February</option>
                        <option value="03">March</option>
                        <option value="04">April</option>
                        <option value="05">May</option>
                        <option value="06">June</option>
                        <option value="07">July</option>
                        <option value="08">August</option>
                        <option value="09">September</option>
                        <option value="10">October</option>
                        <option value="11">November</option>
                        <option value="12">December</option>
                    </select>
                </div>
                <div class="form-group-modern">
                    <i class="fa fa-calendar icon"></i>
                    <select name="birth_year" required>
                        <option value="">Year</option>
                        <?php 
                        $currentYear = date('Y');
                        for($i = $currentYear - 18; $i >= $currentYear - 60; $i--): 
                        ?>
                            <option value="<?php echo $i; ?>"><?php echo $i; ?></option>
                        <?php endfor; ?>
                    </select>
                </div>
            </div>
            
            <!-- Religion -->
            <div class="form-group-modern">
                <i class="fa fa-book icon"></i>
                <select name="religion" required>
                    <option value="">Select Your Religion</option>
                    <option value="Hindu">Hindu</option>
                    <option value="Muslim">Muslim</option>
                    <option value="Christian">Christian</option>
                    <option value="Sikh">Sikh</option>
                    <option value="Buddhist">Buddhist</option>
                    <option value="Jain">Jain</option>
                    <option value="Parsi">Parsi</option>
                    <option value="Jewish">Jewish</option>
                    <option value="Other">Other</option>
                </select>
            </div>
            
            <!-- Religion First -->
            <div class="form-group-modern">
                <i class="fa fa-users icon"></i>
                <select name="religion_first">
                    <option value="">Select Religion First</option>
                    <option value="Yes">Yes, Religion is important</option>
                    <option value="No">No, Open to all religions</option>
                </select>
            </div>
            
            <!-- Mother Tongue -->
            <div class="form-group-modern">
                <i class="fa fa-globe icon"></i>
                <select name="mother_tongue" required>
                    <option value="">Mother Tongue</option>
                    <option value="Hindi">Hindi</option>
                    <option value="English">English</option>
                    <option value="Bengali">Bengali</option>
                    <option value="Telugu">Telugu</option>
                    <option value="Marathi">Marathi</option>
                    <option value="Tamil">Tamil</option>
                    <option value="Gujarati">Gujarati</option>
                    <option value="Urdu">Urdu</option>
                    <option value="Kannada">Kannada</option>
                    <option value="Odia">Odia</option>
                    <option value="Malayalam">Malayalam</option>
                    <option value="Punjabi">Punjabi</option>
                </select>
            </div>
            
            <!-- Country -->
            <div class="form-group-modern">
                <i class="fa fa-flag icon"></i>
                <select name="country" required>
                    <option value="">Country</option>
                    <option value="India">India</option>
                    <option value="USA">USA</option>
                    <option value="UK">UK</option>
                    <option value="Canada">Canada</option>
                    <option value="Australia">Australia</option>
                    <option value="UAE">UAE</option>
                    <option value="Singapore">Singapore</option>
                    <option value="Other">Other</option>
                </select>
            </div>
            
            <!-- Phone Number -->
            <div class="phone-row">
                <div class="form-group-modern country-code">
                    <i class="fa fa-phone icon"></i>
                    <select name="country_code" required>
                        <option value="+91">+91</option>
                        <option value="+1">+1</option>
                        <option value="+44">+44</option>
                        <option value="+61">+61</option>
                        <option value="+971">+971</option>
                    </select>
                </div>
                <div class="form-group-modern phone-number">
                    <i class="fa fa-mobile-alt icon"></i>
                    <input type="tel" name="mobile" placeholder="Enter Your 10 Digit No" required maxlength="10">
                </div>
            </div>
            
            <!-- Email -->
            <div class="form-group-modern">
                <i class="fa fa-envelope icon"></i>
                <input type="email" name="email" placeholder="Enter Your Email Id" required>
            </div>
            
            <!-- Terms and Conditions -->
            <div class="checkbox-group">
                <input type="checkbox" id="terms" name="terms" required>
                <label for="terms">
                    I accept <a href="#">terms & conditions</a> and <a href="#">privacy policy</a>.
                </label>
            </div>
            
            <!-- Submit Button -->
            <button type="submit" class="register-btn">
                Register Now
            </button>
        </form>
        
        <!-- Login Link -->
        <div style="text-align: center; margin-top: 25px; padding-top: 20px; border-top: 1px solid #E5E7EB;">
            <p style="color: #6B7280; margin: 0; font-size: 14px;">
                Already have an account? 
                <a href="login.php" style="color: #FF6B35; text-decoration: none; font-weight: 600;">Login here</a>
            </p>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/jquery.min.js"></script>
    <script src="js/icon-loader.js"></script>
    
    <script>
        // Form validation and submission
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form data
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            // Validate required fields
            const requiredFields = ['profile_by', 'gender', 'first_name', 'last_name', 'birth_day', 'birth_month', 'birth_year', 'religion', 'mother_tongue', 'country', 'country_code', 'mobile', 'email'];
            
            for (let field of requiredFields) {
                if (!data[field] || data[field].trim() === '') {
                    alert(`Please fill in the ${field.replace('_', ' ')} field.`);
                    document.querySelector(`[name="${field}"]`).focus();
                    return;
                }
            }
            
            // Validate email
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(data.email)) {
                alert('Please enter a valid email address.');
                document.querySelector('[name="email"]').focus();
                return;
            }
            
            // Validate mobile number
            const mobileRegex = /^[0-9]{10}$/;
            if (!mobileRegex.test(data.mobile)) {
                alert('Please enter a valid 10-digit mobile number.');
                document.querySelector('[name="mobile"]').focus();
                return;
            }
            
            // Check terms acceptance
            if (!data.terms) {
                alert('Please accept the terms and conditions.');
                document.querySelector('[name="terms"]').focus();
                return;
            }
            
            // Show loading state
            const submitBtn = document.querySelector('.register-btn');
            const originalText = submitBtn.textContent;
            submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Creating Account...';
            submitBtn.disabled = true;
            
            // Simulate form submission
            setTimeout(() => {
                alert('Registration successful! Welcome to our matrimony platform.');
                console.log('Registration data:', data);
                
                // Reset form for demo
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
                
                // In real implementation, redirect to login or dashboard
                // window.location.href = 'login.php';
            }, 2000);
        });
        
        // Auto-format mobile number
        document.querySelector('[name="mobile"]').addEventListener('input', function(e) {
            this.value = this.value.replace(/[^0-9]/g, '');
        });
        
        // Console logging
        console.log('📱 Exact Registration Form Replica Loaded');
        console.log('🎨 Styled icons matching the reference design');
        console.log('✅ Form validation and submission ready');
    </script>
</body>
</html>
